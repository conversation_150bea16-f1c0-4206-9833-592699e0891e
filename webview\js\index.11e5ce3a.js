import { C as ClassStatusEnum, a as SummaryStatusEnum, b as SummarySwitchEnum, _ as __unplugin_components_0, S as SummaryTypeEnum } from "./aiSummary.25e3b83c.js";
import { r as ref, c as computed, $ as onUnmounted, a2 as storeToRefs, d as defineComponent, a3 as useRouter, w as watch, z as onMounted, b as openBlock, m as createElementBlock, j as createBaseVNode, D as createCommentVNode, k as normalizeClass, p as createVNode, u as unref, n as nextTick } from "./bootstrap.ab073eb8.js";
import { l as logger, M as MESSAGE_TYPE, r as reportTrackEvent, k as ZMQ_ENDPOINT_ENUM, v as v4, _ as _export_sfc } from "./index.8338496f.js";
import { R as ResourceTypeEnum, D as DocumentTypeEnum } from "./IResource.516d6004.js";
import { u as useWhiteboardStore, W as WhiteboardThemeEnum } from "./whiteboard.57555170.js";
import { C as CEF_RENDERER_MESSAGE_TYPE, a as QT_RENDERER_MESSAGE_TYPE, b as CEF_NAME } from "./IComm.3bca3c7b.js";
import { c as getFileType } from "./file.7001b00a.js";
import { O as OfficeViewSDK, C as COMMUNICATE_MESSAGE_TYPE } from "./OfficeViewSDK.adef43fb.js";
import { $ as $post, Z as ZmqUtils, a as ZmqMsg, s as showWarning, b as showToast } from "./axios.7d58980a.js";
import { d as useEventBus, e as EventBusEnum } from "./index.d99eb544.js";
import { h as recordResourceProgress } from "./resource.0d7fe94d.js";
import { a as useClassroomStore, u as useDeviceStore } from "./classroom.478fd06d.js";
import { a as openApp } from "./addon.5c4a1973.js";
import { g as get4kScaling } from "./rem.5d1b1196.js";
import { j as getMediaInfoInfo, s as startRecord, k as stopRecord, u as useTimetableStore, l as useConfigStore, c as useVisibilityChange } from "./main.9a6b8281.js";
import { s as summaryStart, g as getAISummaryDetail, r as recordResourceReport, a as getAISummaryByid } from "./aiSummary.9091981f.js";
import "./encryptlong.f30353e7.js";
import "./crypto-js.7319a219.js";
import "./base.649d38c6.js";
import "./hlwhiteboard.b54f17ff.js";
import "./toast.b3d79217.js";
import "./index.d5831e92.js";
import "./toastWidget.f897117d.js";
import "./base.676dddc3.js";
import "./index.1c1fd1ce.js";
import "./index.4d07c967.js";
import "./index.4e3d2b08.js";
import "./typescript.063380fa.js";
import "./use-form-common-props.6b0d7cd2.js";
import "./isUndefined.a6a5e481.js";
import "./stopDrag.9e5623d9.js";
import "./index.091a2398.js";
import "./school.b2d8c825.js";
import "./event.183fce42.js";
import "./el-button.a9e8e4ae.js";
/* empty css                     */import "./directive.aac8de61.js";
import "./theme.80fd53af.js";
var IQtToolClickType = /* @__PURE__ */ ((IQtToolClickType2) => {
  IQtToolClickType2[IQtToolClickType2["NONE"] = 0] = "NONE";
  IQtToolClickType2[IQtToolClickType2["CEF"] = 1] = "CEF";
  IQtToolClickType2[IQtToolClickType2["ELECTRONFUNC"] = 2] = "ELECTRONFUNC";
  IQtToolClickType2[IQtToolClickType2["TOOLBOX"] = 3] = "TOOLBOX";
  IQtToolClickType2[IQtToolClickType2["QT"] = 4] = "QT";
  return IQtToolClickType2;
})(IQtToolClickType || {});
var IQtToolViewType = /* @__PURE__ */ ((IQtToolViewType2) => {
  IQtToolViewType2[IQtToolViewType2["NORMAL"] = 0] = "NORMAL";
  IQtToolViewType2[IQtToolViewType2["GRID"] = 1] = "GRID";
  IQtToolViewType2[IQtToolViewType2["DIVIDE"] = 2] = "DIVIDE";
  return IQtToolViewType2;
})(IQtToolViewType || {});
var ZIndexLevel = /* @__PURE__ */ ((ZIndexLevel2) => {
  ZIndexLevel2[ZIndexLevel2["BOTTOM_LAYER_BASE"] = 0] = "BOTTOM_LAYER_BASE";
  ZIndexLevel2[ZIndexLevel2["CANVAS_LAYER"] = 0] = "CANVAS_LAYER";
  ZIndexLevel2[ZIndexLevel2["BOTTOM_UI_LAYER"] = 100] = "BOTTOM_UI_LAYER";
  ZIndexLevel2[ZIndexLevel2["CEF_MIDDLE_LAYER_BASE"] = 1e3] = "CEF_MIDDLE_LAYER_BASE";
  ZIndexLevel2[ZIndexLevel2["CEF_MEDIA_PREVIEW"] = 1003] = "CEF_MEDIA_PREVIEW";
  ZIndexLevel2[ZIndexLevel2["QT_FLOAT_MENU"] = 2e3] = "QT_FLOAT_MENU";
  ZIndexLevel2[ZIndexLevel2["CEF_THUMBNAIL_LIST"] = 2001] = "CEF_THUMBNAIL_LIST";
  ZIndexLevel2[ZIndexLevel2["CEF_RESOURCE_SELECTOR"] = 2100] = "CEF_RESOURCE_SELECTOR";
  ZIndexLevel2[ZIndexLevel2["CEF_FILE_SAVE"] = 2101] = "CEF_FILE_SAVE";
  ZIndexLevel2[ZIndexLevel2["TOP_LAYER_BASE"] = 3e3] = "TOP_LAYER_BASE";
  ZIndexLevel2[ZIndexLevel2["QT_SIDEBAR_PANEL"] = 3450] = "QT_SIDEBAR_PANEL";
  ZIndexLevel2[ZIndexLevel2["CEF_RANDOM_CALL"] = 3500] = "CEF_RANDOM_CALL";
  ZIndexLevel2[ZIndexLevel2["CEF_TIMER"] = 3500] = "CEF_TIMER";
  ZIndexLevel2[ZIndexLevel2["TEMPORARY_TOP"] = 4e3] = "TEMPORARY_TOP";
  ZIndexLevel2[ZIndexLevel2["CEF_APP_CENTER_FULLSCREEN"] = 4001] = "CEF_APP_CENTER_FULLSCREEN";
  ZIndexLevel2[ZIndexLevel2["CEF_MAGNIFIER"] = 4100] = "CEF_MAGNIFIER";
  ZIndexLevel2[ZIndexLevel2["MODAL_DIALOG"] = 5e3] = "MODAL_DIALOG";
  return ZIndexLevel2;
})(ZIndexLevel || {});
var BoundaryBehavior = /* @__PURE__ */ ((BoundaryBehavior2) => {
  BoundaryBehavior2["Strict"] = "strict";
  BoundaryBehavior2["Clamp"] = "clamp";
  BoundaryBehavior2["Ignore"] = "ignore";
  return BoundaryBehavior2;
})(BoundaryBehavior || {});
var ActionPointerEnum = /* @__PURE__ */ ((ActionPointerEnum2) => {
  ActionPointerEnum2["PREVIOUS"] = "previous";
  ActionPointerEnum2["NEXT"] = "next";
  ActionPointerEnum2["JUMP"] = "jump";
  return ActionPointerEnum2;
})(ActionPointerEnum || {});
function useArrayPointer(initialData = []) {
  const data = ref(initialData);
  const currentIndex = ref(initialData.length > 0 ? 0 : -1);
  const isCircular = ref(false);
  const boundaryBehavior = ref(
    "strict"
    /* Strict */
  );
  const current = computed(() => {
    return isValidIndex() ? data.value[currentIndex.value] : null;
  });
  const hasNext = computed(() => {
    return currentIndex.value < data.value.length - 1;
  });
  const hasPrevious = computed(() => {
    return currentIndex.value > 0;
  });
  const getIndex = computed(() => {
    return currentIndex.value;
  });
  const getLength = computed(() => {
    return data.value.length;
  });
  function jumpTo(index2) {
    if (index2 === currentIndex.value)
      return;
    if (index2 < 0 || index2 >= data.value.length) {
      switch (boundaryBehavior.value) {
        case "strict":
          throw new Error(`Invalid index: ${index2}`);
        case "clamp":
          index2 = Math.max(0, Math.min(index2, data.value.length - 1));
          break;
        case "ignore":
          return;
      }
    }
    currentIndex.value = index2;
  }
  function next() {
    if (hasNext.value) {
      jumpTo(currentIndex.value + 1);
    } else if (isCircular.value && data.value.length > 0) {
      jumpTo(0);
    }
  }
  function previous() {
    if (hasPrevious.value) {
      jumpTo(currentIndex.value - 1);
    } else if (isCircular.value && data.value.length > 0) {
      jumpTo(data.value.length - 1);
    }
  }
  function addElement(element, position) {
    const insertAt = position ?? data.value.length;
    data.value.splice(insertAt, 0, element);
    adjustIndexAfterAddition(insertAt);
  }
  function removeCurrent() {
    if (!isValidIndex())
      return;
    data.value.splice(currentIndex.value, 1);
    adjustIndexAfterRemoval();
  }
  function updateCurrent(newValue) {
    if (isValidIndex()) {
      data.value[currentIndex.value] = newValue;
    }
  }
  function resetData(newData, index2) {
    data.value = newData;
    if (index2 !== void 0) {
      switch (boundaryBehavior.value) {
        case "strict":
          if (index2 < 0 || index2 >= newData.length) {
            throw new Error(`Invalid index: ${index2}`);
          }
          break;
        case "clamp":
          index2 = Math.max(0, Math.min(index2, newData.length - 1));
          break;
        case "ignore":
          index2 = newData.length > 0 ? 0 : -1;
          break;
      }
    }
    currentIndex.value = index2 !== void 0 ? index2 : newData.length > 0 ? 0 : -1;
  }
  function setCircularMode(enabled) {
    isCircular.value = enabled;
  }
  function setBoundaryBehavior(mode) {
    boundaryBehavior.value = mode;
  }
  function isValidIndex() {
    return currentIndex.value >= 0 && currentIndex.value < data.value.length;
  }
  function adjustIndexAfterAddition(insertPosition) {
    if (currentIndex.value >= insertPosition) {
      currentIndex.value++;
    }
  }
  function adjustIndexAfterRemoval() {
    if (currentIndex.value >= data.value.length) {
      currentIndex.value = Math.max(0, data.value.length - 1);
    }
  }
  return {
    currentIndex,
    data,
    current,
    jumpTo,
    next,
    previous,
    hasNext,
    hasPrevious,
    getIndex,
    getLength,
    addElement,
    removeCurrent,
    updateCurrent,
    resetData,
    setCircularMode,
    setBoundaryBehavior
  };
}
const sysUrl = "/zhhb-system";
function getCurrentUserToolList(data) {
  return $post(`${sysUrl}/v1/tool/get_tools`, data);
}
function useHeartbeat({
  interval = 1e3,
  // 默认心跳间隔为1秒
  onTick = () => {
  },
  // 默认空函数
  onDisconnect = () => {
  },
  // 默认空函数
  customCheck = () => true
  // 默认连接始终有效
}) {
  const isRunning = ref(false);
  const missedPings = ref(0);
  const isConnected = ref(true);
  let timer = null;
  let isChecking = false;
  const startHeartbeat = () => {
    if (isRunning.value)
      return;
    isRunning.value = true;
    isConnected.value = true;
    missedPings.value = 0;
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    checkHeartbeat();
  };
  const checkHeartbeat = async () => {
    if (isChecking)
      return;
    isChecking = true;
    try {
      onTick();
      missedPings.value += 1;
      const connectionStatus = await customCheck();
      isConnected.value = connectionStatus;
      logger.debug(connectionStatus);
      if (!connectionStatus) {
        onDisconnect();
      } else {
        missedPings.value = 0;
      }
    } catch (error) {
      logger.error("【heartbeat】", "心跳检测发生错误:", error);
      onDisconnect();
    } finally {
      isChecking = false;
      if (isRunning.value) {
        timer = setTimeout(checkHeartbeat, interval);
      }
    }
  };
  const stopHeartbeat = () => {
    if (timer) {
      clearTimeout(timer);
    }
    isRunning.value = false;
  };
  const resetMissedPings = () => {
    missedPings.value = 0;
  };
  const getMissedPings = computed(() => missedPings.value);
  onUnmounted(() => {
    if (timer) {
      clearTimeout(timer);
    }
  });
  return {
    isRunning,
    isConnected,
    startHeartbeat,
    stopHeartbeat,
    resetMissedPings,
    // 返回重置未响应次数的函数
    getMissedPings
    // 返回检查次数，只读
  };
}
async function handleGenerateAISummary(aiTaskType) {
  let macAddress = null;
  const aiClassSummaryId = null;
  let isRecording = false;
  const isFlag = await setAISummary(
    3
    /* BOTH */
  );
  console.log("AI总结开始上报", isFlag);
  if (!isFlag && aiTaskType !== ClassStatusEnum.GIVE_UP_SUMMARY) {
    return Promise.resolve({
      isRecording: false,
      aiClassSummaryId
    });
  }
  if (aiTaskType === ClassStatusEnum.START_CLASS) {
    const res = await startAudioProcessing();
    console.log("AI总结开始上报录音", res);
    if (!res.success)
      return Promise.resolve({
        isRecording: false,
        aiClassSummaryId
      });
    isRecording = res.success;
    macAddress = res.success ? res.macAddress : null;
    console.log("AI总结开始上报录音", isRecording, macAddress);
  } else {
    const classroomStore = useClassroomStore();
    const { classTeacher } = storeToRefs(classroomStore);
    if (classTeacher.value.lessonId?.length && aiTaskType === ClassStatusEnum.FINISH_CLASS) {
      return Promise.resolve({
        isRecording: true,
        aiClassSummaryId: localStorage.getItem("aiClassSummaryId")
      });
    }
    const res = await endAudioProcessing();
    if (!res.success)
      return Promise.resolve({
        isRecording: false,
        aiClassSummaryId
      });
    isRecording = res.success;
    macAddress = res.success ? res.macAddress : null;
  }
  console.log("AI总结开始上报掉接口", aiTaskType, macAddress);
  const aiSummaryRes = await ajaxAISummaryStart(aiTaskType, macAddress);
  if (aiSummaryRes?.isRecording === false) {
    isRecording = false;
  }
  return Promise.resolve({
    isRecording,
    aiClassSummaryId: localStorage.getItem("aiClassSummaryId")
  });
}
async function startAudioProcessing() {
  console.log("执行。。。。。开始录音");
  try {
    const classroomStore = useClassroomStore();
    const { classTeacher } = storeToRefs(classroomStore);
    const { saasSchoolId, saasClassId, saasCampusId } = classTeacher.value;
    const { currentMediaInfo } = await getMediaInfoInfo();
    if (!currentMediaInfo || currentMediaInfo && !currentMediaInfo.audioinput) {
      return Promise.resolve({
        success: false,
        macAddress: null
      });
    }
    const res = await startRecord({
      micLabel: currentMediaInfo.audioinput.label,
      classInfo: {
        saasSchoolId,
        saasCampusId,
        saasClassId
      }
    });
    console.log("resstartRecord", res);
    return Promise.resolve({
      success: true,
      ...res.data
    });
  } catch (error) {
    console.error("开始录音失败", error);
    return Promise.resolve({
      success: false,
      macAddress: null
    });
  }
}
async function endAudioProcessing() {
  try {
    const res = await stopRecord();
    return Promise.resolve({
      success: true,
      ...res.data
    });
  } catch (error) {
    console.error("结束录音失败", error);
    return Promise.resolve({
      success: false,
      macAddress: null
    });
  }
}
async function ajaxAISummaryStart(aiTaskType, macAddress) {
  try {
    const classroomStore = useClassroomStore();
    const { classTeacher } = storeToRefs(classroomStore);
    const timetableStore = useTimetableStore();
    console.log(
      "AI课堂小结功能已开启，开始上报",
      classTeacher.value,
      timetableStore.currentSession,
      timetableStore.currentLesson
    );
    const res = await summaryStart({
      saasSchoolId: classTeacher.value.saasSchoolId,
      saasClassId: classTeacher.value.saasClassId,
      saasUserId: classTeacher.value.saasUserId,
      saasSubjectId: classTeacher.value.saasSubjectId,
      saasSubjectCode: classTeacher.value.saasSubjectCode,
      saasSubjectName: classTeacher.value.saasSubjectName,
      sessionTimeStart: classTeacher.value.lessonId?.length ? timetableStore.currentSession?.sessionTimeStart + ":00" : null,
      sessionTimeEnd: classTeacher.value.lessonId?.length ? timetableStore.currentSession?.sessionTimeEnd + ":00" : null,
      aiTaskType,
      aiClassSummaryId: aiTaskType === ClassStatusEnum.START_CLASS ? null : localStorage.getItem("aiClassSummaryId"),
      lessonId: classTeacher.value.lessonId,
      macAddress
    });
    console.log("res", res);
    if (aiTaskType === ClassStatusEnum.START_CLASS) {
      const previousAiClassSummaryId = localStorage.getItem("aiClassSummaryId");
      if (previousAiClassSummaryId !== res.data.aiClassSummaryId) {
        localStorage.setItem("aiClassSummaryId", res.data.aiClassSummaryId ?? "");
      } else {
        const { data } = await getAISummaryDetail({
          aiClassSummaryId: localStorage.getItem("aiClassSummaryId")
        });
        if (data.aiSummaryStatus === SummaryStatusEnum.SUCCEED || data.aiSummaryStatus === SummaryStatusEnum.DEFEATED) {
          return Promise.resolve({
            success: true,
            isRecording: false
          });
        }
      }
    } else if (!classTeacher.value.lessonId?.length) {
    }
    return Promise.resolve({
      success: true,
      isRecording: true
    });
  } catch (error) {
    console.error("AI课堂小结上报失败", error);
    return Promise.resolve({
      success: false,
      isRecording: false
    });
  }
}
var SetAISummaryEnum = /* @__PURE__ */ ((SetAISummaryEnum2) => {
  SetAISummaryEnum2[SetAISummaryEnum2["TARGET_SUBJECT"] = 1] = "TARGET_SUBJECT";
  SetAISummaryEnum2[SetAISummaryEnum2["AI_SWITCH"] = 2] = "AI_SWITCH";
  SetAISummaryEnum2[SetAISummaryEnum2["BOTH"] = 3] = "BOTH";
  return SetAISummaryEnum2;
})(SetAISummaryEnum || {});
async function setAISummary(type) {
  const classroomStore = useClassroomStore();
  const { classTeacher } = storeToRefs(classroomStore);
  const configStore = useConfigStore();
  const configList = configStore.getBaseConfig("subject");
  const isTargetSubject = Object.values(configList ?? {}).includes(
    classTeacher.value.disciplineCode || ""
  );
  const isOpenAISummary = configStore.isOpenAISummary;
  const isSwitchOpen = isOpenAISummary === SummarySwitchEnum.OPEN;
  console.log("configStore", isSwitchOpen, configStore);
  switch (type) {
    case 1:
      return isTargetSubject;
    case 2:
      return isSwitchOpen;
    case 3:
      return isTargetSubject && isSwitchOpen;
    default:
      return isTargetSubject && isSwitchOpen;
  }
}
const _hoisted_1 = {
  id: "classroom-container",
  class: "classroom-container"
};
const _hoisted_2 = ["src"];
const _hoisted_3 = {
  key: 0,
  class: "qt-connect-error"
};
const _hoisted_4 = {
  key: 1,
  class: "generate-img-box"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const whiteboardStore = useWhiteboardStore();
    const { historyIndex } = storeToRefs(whiteboardStore);
    const officeViewRef = ref(null);
    const classroomStore = useClassroomStore();
    const { classTeacher } = storeToRefs(classroomStore);
    const resourceManager = useArrayPointer(classroomStore.openResourceList);
    const resourcePageManager = useArrayPointer([]);
    const { current: currentResource, currentIndex: currentResourceIndex } = resourceManager;
    const {
      current: currentResourcePage,
      getLength: resourcePageLength,
      currentIndex: currentResourcePageIndex
    } = resourcePageManager;
    resourcePageManager.setBoundaryBehavior(BoundaryBehavior.Ignore);
    const officeViewSdk = new OfficeViewSDK(officeViewRef.value);
    const officeViewUrl = ref("");
    const isViewOffice = computed(() => {
      return currentResource.value?.type && [ResourceTypeEnum.PDF, ResourceTypeEnum.PPT].includes(currentResource.value?.type);
    });
    const isViewSource = computed(() => {
      return currentResource.value?.type && [
        ResourceTypeEnum.AUDIO,
        ResourceTypeEnum.IMAGE,
        ResourceTypeEnum.VIDEO,
        ResourceTypeEnum.UNKNOWN
      ].includes(currentResource.value?.type);
    });
    const whiteboardTheme = computed(() => {
      return isViewOffice.value || isViewSource.value ? WhiteboardThemeEnum.TRANSPARENT : WhiteboardThemeEnum.GREEN;
    });
    const whiteboardWidth = ref(1920);
    const whiteboardHeight = ref(1080);
    const router = useRouter();
    const { isConnected, getMissedPings, startHeartbeat, resetMissedPings, stopHeartbeat } = useHeartbeat({
      interval: 2e3,
      customCheck() {
        return ZmqUtils.queryConnectStatus(ZMQ_ENDPOINT_ENUM.HL_WHITE_BOARD);
      },
      onDisconnect() {
        if (getMissedPings.value > 5) {
          logger.debug("【classroom】", "白板断开连接，断开次数：", isConnected.value, getMissedPings.value);
        }
      }
    });
    const isQTConnectError = computed(() => {
      return !isConnected.value && getMissedPings.value > 5;
    });
    watch(isQTConnectError, () => {
      if (isQTConnectError) {
        logger.error("【classroom】", "白板断开连接，断开次数：", isConnected.value, getMissedPings.value);
      }
    });
    watch(currentResourceIndex, () => {
      classroomStore.updateCurrentResourceIndex(currentResourceIndex.value);
    });
    classroomStore.startClass();
    const onExitHome = async () => {
      if (resourceReportData.value?.pageIndex) {
        ajaxRecordResourceReport(resourceReportData.value?.pageIndex, true);
      }
      handleGenerateAISummary(ClassStatusEnum.FINISH_CLASS);
      const _currentResource = currentResource.value;
      if (_currentResource) {
        _currentResource.pageCurrentIndex = resourcePageManager.getIndex.value;
      }
      classroomStore.endClass();
      router.replace("/workbench");
    };
    const switchResource = (type, index2 = 0) => {
      const prevResource = currentResource.value;
      if (prevResource) {
        prevResource.pageCurrentIndex = resourcePageManager.getIndex.value;
      }
      switch (type) {
        case ActionPointerEnum.PREVIOUS:
          resourceManager.previous();
          notifyQtResourceStatusChange();
          break;
        case ActionPointerEnum.NEXT:
          resourceManager.next();
          notifyQtResourceStatusChange();
          break;
        case ActionPointerEnum.JUMP:
          resourceManager.jumpTo(index2);
          notifyQtResourceStatusChange();
          break;
      }
      const switchResource2 = currentResource.value;
      if (!switchResource2) {
        return;
      }
      officeViewSdk.off(COMMUNICATE_MESSAGE_TYPE.DOMContentLoaded);
      const { id: resourceId, type: resourceType, info, pages, pageCurrentIndex } = switchResource2;
      const documentHtmlUrl = info?.documentHtmlUrl;
      switch (resourceType) {
        case ResourceTypeEnum.PDF:
        case ResourceTypeEnum.PPT:
          if (!switchResource2.loaded) {
            officeViewUrl.value = OfficeViewSDK.genOfficeViewSrc(
              resourceId,
              documentHtmlUrl,
              pageCurrentIndex
            );
            console.log("【classRoom】预览地址", officeViewUrl.value);
            resourcePageManager.resetData(pages, pageCurrentIndex);
            officeViewSdk.once(COMMUNICATE_MESSAGE_TYPE.DOMContentLoaded, (data) => {
              if (switchResource2.id === data.documentId) {
                switchResource2.width = data.width;
                switchResource2.height = data.height;
                whiteboardWidth.value = data.width;
                whiteboardHeight.value = data.height;
                switchResource2.pages = genResoucePages(data.totalSlides);
                resourcePageManager.resetData(switchResource2.pages, pageCurrentIndex);
                switchResourcePage(ActionPointerEnum.JUMP, pageCurrentIndex);
                switchResource2.loaded = true;
                onChangeResourcePage({
                  slide: pageCurrentIndex,
                  documentId: switchResource2.id
                });
              }
            });
          } else {
            whiteboardWidth.value = switchResource2.width;
            whiteboardHeight.value = switchResource2.height;
            resourcePageManager.resetData(pages, pageCurrentIndex);
            switchResourcePage(ActionPointerEnum.JUMP, pageCurrentIndex);
            officeViewUrl.value = OfficeViewSDK.genOfficeViewSrc(
              resourceId,
              documentHtmlUrl,
              pageCurrentIndex
            );
          }
          break;
        default:
          officeViewUrl.value = "";
          whiteboardWidth.value = document.body.clientWidth;
          whiteboardHeight.value = document.body.clientHeight;
          resourcePageManager.resetData(pages, pageCurrentIndex);
          switchResourcePage(ActionPointerEnum.JUMP, pageCurrentIndex);
          break;
      }
    };
    const switchResourcePage = (type, index2 = 0) => {
      switch (type) {
        case ActionPointerEnum.PREVIOUS:
          resourcePageManager.previous();
          break;
        case ActionPointerEnum.NEXT:
          resourcePageManager.next();
          break;
        case ActionPointerEnum.JUMP:
          resourcePageManager.jumpTo(index2);
          break;
      }
    };
    const genResoucePages = (total) => {
      return Array.from({ length: total }, (_, index2) => {
        return {
          id: v4(),
          index: index2,
          wbHistory: [],
          wbHistoryCurrentIndex: 0
        };
      });
    };
    const addResource = (type, info, total = 1, pageIndex = 0) => {
      const resource = {
        id: v4(),
        type,
        info: {
          ...info
        },
        pages: genResoucePages(total),
        pageCurrentIndex: pageIndex,
        loaded: false,
        width: 0,
        height: 0,
        saved: false
      };
      const index2 = resourceManager.getLength.value;
      resourceManager.addElement(resource);
      switchResource(ActionPointerEnum.JUMP, index2);
      return resource;
    };
    const onHistoryIndexChange = (index2) => {
      const resourcePage = currentResourcePage.value;
      if (resourcePage) {
        resourcePage.wbHistoryCurrentIndex = index2;
      }
    };
    watch(historyIndex, onHistoryIndexChange);
    const resetWhiteboard = () => {
    };
    const onAddBoard = () => {
      let count = 0;
      classroomStore.openResourceList.forEach((item) => {
        if (item.type === ResourceTypeEnum.WHITEBOARD) {
          count++;
        }
      });
      if (count >= 50) {
        showWarning("白板数量已达上限");
        return;
      }
      addResource(ResourceTypeEnum.WHITEBOARD, {
        fileId: "",
        documentId: v4(),
        documentName: "白板",
        documentUrl: "",
        thumbnailUrl: ""
      });
      whiteboardWidth.value = document.body.clientWidth;
      whiteboardHeight.value = document.body.clientHeight;
    };
    const onPrevFrame = () => {
      let reqId = null;
      const type = currentResource.value?.type;
      console.log("上一页", type);
      if (type && type === ResourceTypeEnum.PDF) {
        reqId = officeViewSdk.postMessageToIframe(COMMUNICATE_MESSAGE_TYPE.PREV_SLIDE);
      } else {
        reqId = officeViewSdk.postMessageToIframe(COMMUNICATE_MESSAGE_TYPE.PREV);
      }
      if (!reqId) {
        throw "切换上一帧失败, 空请求ID";
      }
      officeViewSdk.invoke(COMMUNICATE_MESSAGE_TYPE.GET_CURRENT_STATUS);
    };
    const onNextFrame = () => {
      let reqId = null;
      const type = currentResource.value?.type;
      console.log("下一页", type);
      if (type && type === ResourceTypeEnum.PDF) {
        reqId = officeViewSdk.postMessageToIframe(COMMUNICATE_MESSAGE_TYPE.NEXT_SLIDE);
      } else {
        reqId = officeViewSdk.postMessageToIframe(COMMUNICATE_MESSAGE_TYPE.NEXT);
      }
      if (!reqId) {
        throw "切换下一帧失败, 空请求ID";
      }
      officeViewSdk.invoke(COMMUNICATE_MESSAGE_TYPE.GET_CURRENT_STATUS);
    };
    const onPrevResource = () => {
      if (!resourceManager.hasPrevious.value) {
        return false;
      }
      switchResource(ActionPointerEnum.PREVIOUS);
      return true;
    };
    const onNextResource = () => {
      if (!resourceManager.hasNext.value) {
        return false;
      }
      switchResource(ActionPointerEnum.NEXT);
      return true;
    };
    const resourceReportData = ref(null);
    const ajaxRecordResourceReport = async (slide, isExit = false) => {
      const isFlag = await setAISummary(SetAISummaryEnum.BOTH);
      if (!isFlag) {
        return;
      }
      console.log("【监听资源切换】222", currentResource.value);
      if (!resourceReportData.value?.startTime) {
        const startTime = Date.now();
        console.log("【监听资源切换】记录开始时间", new Date(startTime));
        resourceReportData.value = {
          aiClassSummaryId: localStorage.getItem("aiClassSummaryId"),
          resourceId: currentResource.value?.info?.documentId || "",
          resourceName: currentResource.value?.info?.documentName || "",
          resourceType: currentResource.value?.type,
          resourceUrl: currentResource.value?.info?.documentUrl,
          pageIndex: slide,
          startTime,
          durationSeconds: 0
        };
        return;
      }
      console.log(
        "【监听资源切换】判断ppt是否发生了切换",
        resourceReportData.value,
        slide,
        currentResource.value?.info?.documentId
      );
      if (resourceReportData.value.pageIndex === slide && resourceReportData.value.resourceId === currentResource.value?.info?.documentId && !isExit) {
        return;
      }
      const endTime = Date.now();
      const durationSeconds = (endTime - resourceReportData.value.startTime) / 1e3;
      console.log("【监听资源切换】记录结束时间", new Date(endTime), durationSeconds);
      if (durationSeconds > 3) {
        console.log("【监听资源切换】333上报", durationSeconds);
        await recordResourceReport({
          saasUserId: classTeacher.value.saasUserId,
          resourceReport: {
            ...resourceReportData.value,
            durationSeconds
          }
        });
      }
      resourceReportData.value = {
        aiClassSummaryId: localStorage.getItem("aiClassSummaryId"),
        resourceId: currentResource.value?.info?.documentId || "",
        resourceName: currentResource.value?.info?.documentName || "",
        resourceType: currentResource.value?.type,
        resourceUrl: currentResource.value?.info?.documentUrl,
        pageIndex: slide,
        startTime: Date.now(),
        durationSeconds: 0
      };
    };
    const onChangeResourcePage = (data) => {
      logger.debug("【监听资源切换】onChangeResourcePage", data);
      console.log("【监听资源切换】1111 onChangeResourcePage", data, currentResource.value?.id);
      if (currentResource.value?.id === data.documentId && currentResource.value.loaded) {
        console.log("【监听资源切换】触发");
        const total = resourcePageLength.value;
        const slide = data.slide;
        switchResourcePage(ActionPointerEnum.JUMP, slide);
        const classTeacherVal = classTeacher.value;
        const currentDocumentId = currentResource.value?.info?.documentId || "";
        const teachingProgress = total === 0 ? 0 : (slide + 1) / total;
        recordResourceProgress({
          saasUserId: classTeacherVal.saasUserId,
          saasClassId: classTeacherVal.saasClassId,
          saasSchoolId: classTeacherVal.saasSchoolId,
          fileType: currentResource.value?.type,
          fileId: currentDocumentId,
          total,
          anchorPoint: slide,
          teachingProgress: (teachingProgress > 1 ? 1 : teachingProgress).toFixed(2)
        });
        notifyQtResourceStatusChange();
      }
    };
    const getCurrentResourceInfo = () => {
      const _currentResource = currentResource.value;
      return {
        resourceId: _currentResource?.id || "",
        // 资源id，uuidv4生成得
        documentId: _currentResource?.info?.documentId || "",
        // 文档id，是业务数据id
        resourceType: _currentResource?.type,
        slide: resourceManager.currentIndex.value + 1,
        // 当前资源第几个
        total: resourceManager.getLength.value || 0,
        // 打开资源总数
        pageSlide: currentResourcePageIndex.value + 1,
        // 当前资源第几页
        pageTotal: resourcePageLength.value || 0
      };
    };
    const notifyQtResourceStatusChange = () => {
      const _currentResource = currentResource.value;
      if (_currentResource) {
        ZmqUtils.sendRequest(
          QT_RENDERER_MESSAGE_TYPE.QT_UPDATE_RESOURCE_STATUS,
          getCurrentResourceInfo()
        );
        ZmqUtils.sendRequestQtCef(
          CEF_NAME.THUMBNAIL_CEF,
          CEF_RENDERER_MESSAGE_TYPE.THUMBNAIL_RESOURCE_PAGE_CHANGE,
          getCurrentResourceInfo()
        );
      }
    };
    officeViewSdk.on(COMMUNICATE_MESSAGE_TYPE.PAGE_CHANGED_EVENT, onChangeResourcePage);
    const onCloseResource = () => {
      resourceManager.removeCurrent();
      if (resourceManager.getLength.value <= 0) {
        onAddBoard();
      }
      switchResource(ActionPointerEnum.JUMP, resourceManager.currentIndex.value);
    };
    const onJumpToPage = (index2) => {
      if (index2 !== currentResourcePageIndex.value) {
        officeViewSdk.postMessageToIframe(COMMUNICATE_MESSAGE_TYPE.JUMP_TO, {
          slide: index2,
          step: 0
        });
      }
    };
    const onSelectResource = async (data) => {
      const { resource, type } = data;
      const findResourceIndex = classroomStore.openResourceList.findIndex((item) => {
        return item.info?.documentId === resource.documentId;
      });
      if (findResourceIndex > -1) {
        switchResource(ActionPointerEnum.JUMP, findResourceIndex);
        return;
      }
      const fileType = getFileType(resource.documentUrl);
      let anchorPoint = resource.anchorPoint || 0;
      switch (type) {
        case DocumentTypeEnum.COURSEWARE:
          console.log(
            `【自动跳转逻辑】当前索引：${anchorPoint}，当前页：${anchorPoint + 1}，总页数：${resource.total}`
          );
          if (resource.total && resource.total > 0 && anchorPoint < resource.total - 1) {
            anchorPoint++;
            showToast(`记录到您上一次播放到第${anchorPoint}页，自动从第${anchorPoint + 1}页继续播放。`);
          }
          addResource(fileType, resource, 0, anchorPoint);
          whiteboardStore.activeCoursewareSelectTool();
          break;
        case DocumentTypeEnum.MATERIAL:
          if (fileType === ResourceTypeEnum.IMAGE) {
            const url = new URL(resource.documentUrl);
            url.searchParams.set("x-oss-process", "image/resize,w_2626,h_1476/quality,q_80");
            const res = await ZmqUtils.sendRequest(
              QT_RENDERER_MESSAGE_TYPE.QT_INSERT_IMAGE_WHITEBOARD,
              {
                imageUrl: url.toString()
              }
            );
            logger.info("【classroom】", "通知QT白板插入图片结果", res);
          } else {
            const scale = get4kScaling();
            const cefConfig = {
              toolName: `素材-${resource.documentName}-` + Date.now(),
              toolIcon: "",
              viewType: IQtToolViewType.NORMAL,
              clickType: IQtToolClickType.CEF,
              url: `http://domainname/src/widget/SourceResourceView/index.html?type=${fileType}&url=${encodeURIComponent(resource.documentUrl)}`,
              geometry: [0, 0, 100 * scale, 100 * scale],
              zlevel: ZIndexLevel.CEF_MEDIA_PREVIEW,
              showToolBox: false,
              showDragBar: true,
              showControlBar: true
            };
            switch (fileType) {
              case ResourceTypeEnum.AUDIO:
                cefConfig.geometry = [1329.5 * scale, 516.5 * scale, 1470 * scale, 1049.6 * scale];
                break;
              case ResourceTypeEnum.VIDEO:
                cefConfig.geometry = [649 * scale, 292.3 * scale, 2801.5 * scale, 1576.6 * scale];
                break;
            }
            ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_CREATE_CEF, cefConfig);
          }
          break;
      }
    };
    watch(officeViewRef, () => {
      if (isViewOffice.value) {
        officeViewSdk.switchIframeDom(officeViewRef.value);
      }
    });
    const getQtAppRightToolsConfig = async (teacher) => {
      const scale = get4kScaling();
      const rightToolsConfig = [];
      const { width: windowWidth, height: windowHeight } = window.screen;
      try {
        const builtToolMap = {};
        builtToolMap["放大镜"] = {
          toolName: "放大镜",
          toolIcon: ":/images/side/side_amplifier.svg",
          viewType: IQtToolViewType.NORMAL,
          clickType: IQtToolClickType.QT,
          url: `http://domainname/src/widget/Magnifier/index.html`,
          geometry: [0, 0, windowWidth, windowHeight],
          zlevel: ZIndexLevel.CEF_MAGNIFIER,
          showToolBox: true,
          // 放大镜在工具箱中显示
          fullscreen: true
        };
        builtToolMap["随机点名"] = {
          toolName: "随机点名",
          toolIcon: ":/images/side/side_random.svg",
          viewType: IQtToolViewType.NORMAL,
          clickType: IQtToolClickType.CEF,
          url: `http://domainname/src/widget/RollCallPanel/index.html?saasSchoolId=${teacher.saasSchoolId}&saasClassId=${teacher.saasClassId}`,
          geometry: [
            Math.ceil(3314 * scale),
            Math.ceil(826 * scale),
            Math.ceil(631.5 * scale),
            Math.ceil(561.5 * scale)
          ],
          zlevel: ZIndexLevel.CEF_RANDOM_CALL,
          showToolBox: true,
          // 随机点名在工具箱中显示
          showControlBar: true,
          showDragBar: true
        };
        builtToolMap["计时器"] = {
          toolName: "计时器",
          toolIcon: ":/images/side/side_time.svg",
          viewType: IQtToolViewType.NORMAL,
          clickType: IQtToolClickType.CEF,
          url: "http://domainname/src/widget/TickTock/index.html",
          geometry: [
            Math.ceil(3370 * scale),
            Math.ceil(826 * scale),
            Math.ceil(535.5 * scale),
            Math.ceil(501 * scale) + 5
          ],
          zlevel: ZIndexLevel.CEF_TIMER,
          showToolBox: true,
          // 计时器在工具箱中显示
          showControlBar: true,
          showDragBar: true,
          showFullScreenButton: true
        };
        const { data } = await getCurrentUserToolList({
          saasUserId: teacher.saasUserId
        });
        if (Array.isArray(data?.teachToolList)) {
          data.teachToolList.forEach((item) => {
            const { toolName } = item;
            const tool = builtToolMap[toolName];
            if (tool) {
              rightToolsConfig.push(tool);
            } else {
              logger.warn(`[ToolBar]工具不存在`, item);
            }
          });
        }
      } catch (error) {
        logger.error("[getQtAppRightToolsConfig]", error);
      }
      return rightToolsConfig;
    };
    const onQtConnencted = async (teacher) => {
      try {
        await ZmqUtils.sendRequest("beginclass", {
          ...teacher,
          rightToolsConfig: await getQtAppRightToolsConfig(teacher)
        });
        notifyQtResourceStatusChange();
        ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_OPEN_RESOURCE_DIALOG, {});
        logger.info("【QT】", "qt应用初始化成功");
      } catch (e) {
        logger.error("【QT】", "开始上课初始化失败", e);
      }
    };
    const cancelList = [];
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_SELECT_RESOURCE, async (req) => {
        onSelectResource(req.data);
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_ADD_BOARD, async () => {
        onAddBoard();
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    useEventBus(EventBusEnum.MOBILE_UPLOAD_SUCCESS, (data) => {
      ZmqUtils.sendRequestQtCef(
        CEF_NAME.RESOURCE_DIALOG_CEF,
        CEF_RENDERER_MESSAGE_TYPE.RESOURCEDIALOG_REFRESH_RESOURCE_LIST,
        data
      ).then(() => {
        logger.info("【ResourceDialog】", "资源更新成功");
      }).catch(() => {
        logger.error("【ResourceDialog】", "资源更新失败");
      });
    });
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_PREV_FRAME, async () => {
        onPrevFrame();
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_NEXT_FRAME, async () => {
        onNextFrame();
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_PREV_RESOURCE, async () => {
        const res = onPrevResource();
        return ZmqMsg.newResponse({
          message: res ? "触发成功" : "下一个资源不存在"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_NEXT_RESOURCE, async () => {
        const res = onNextResource();
        return ZmqMsg.newResponse({
          message: res ? "触发成功" : "下一个资源不存在"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CLOSE_RESOURCE, async () => {
        onCloseResource();
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_JUMP_TO_PAGE, async (req) => {
        const index2 = req.data?.index;
        onJumpToPage(index2);
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS, async () => {
        return ZmqMsg.newResponse({
          teacher: classTeacher.value,
          currentResource: JSON.parse(JSON.stringify(currentResource.value)),
          currentResourceIndex: currentResourceIndex.value,
          currentResourcePageIndex: currentResourcePageIndex.value
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(MESSAGE_TYPE.ZMQ_CONNECTED, async () => {
        onQtConnencted(classTeacher.value);
        resetMissedPings();
        logger.info("订阅zmq连接成功-onQtConnencted");
        return ZmqMsg.newResponse({ message: "订阅zmq连接成功-onQtConnencted" });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_EXIT, async () => {
        onExitHome();
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_RESOURCE_SAVED, async () => {
        if (currentResource.value) {
          currentResource.value.saved = true;
        }
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(QT_RENDERER_MESSAGE_TYPE.CLASSROOM_RESOURCE_TRACE_CHANGE, async () => {
        if (currentResource.value) {
          currentResource.value.saved = false;
        }
        return ZmqMsg.newResponse({
          message: "触发成功"
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(QT_RENDERER_MESSAGE_TYPE.CLASSROOM_RESOURCE_SAVED_STATUS, async () => {
        return ZmqMsg.newResponse({
          saved: currentResource.value?.saved
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(QT_RENDERER_MESSAGE_TYPE.CLASSROOM_OPEN_MAGNIFIER, async () => {
        reportTrackEvent("CLICK", {
          text: "放大镜",
          dataset: {
            text: "星讲台-放大镜",
            module: "星讲台-放大镜-放大镜"
          }
        });
        return ZmqMsg.newResponse({
          message: "上报成功"
        });
      })
    );
    useVisibilityChange({
      onVisibilityChange(isVisible) {
        if (isVisible) {
          ZmqUtils.sendRequest(MESSAGE_TYPE.WIN_STORE, {
            show: true
          });
        } else {
          ZmqUtils.sendRequest(MESSAGE_TYPE.WIN_MINIMIZE, {
            show: false
          });
        }
      }
    });
    const openQtApp = async () => {
      try {
        const isOpen = await openApp("./resources/hl-whiteboard-qt/hl-whiteboard-qt.exe");
        ZmqUtils.sendRequest(MESSAGE_TYPE.WIN_STORE, {
          show: true
        });
        if (!isOpen) {
          throw "发送启动失败";
        }
        logger.info("【QT】", "发送启动成功");
      } catch (error) {
        logger.error("【QT】", "发送启动消息异常", error);
      }
    };
    openQtApp();
    const onResetQt = () => {
      openQtApp();
      if (showAIOctopus.value) {
        ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_SHOW_AI_SUMMARY_OCTOPUS, {});
      } else {
        ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_CLOSE_AI_SUMMARY_OCTOPUS, {});
      }
    };
    onMounted(async () => {
      try {
        startHeartbeat();
        const { openResourceList, currentResourceIndex: currentResourceIndex2 } = classroomStore;
        if (openResourceList.length > 0) {
          switchResource(ActionPointerEnum.JUMP, currentResourceIndex2);
        } else {
          const nullArray = [];
          resourceManager.resetData(nullArray);
          classroomStore.openResourceList = nullArray;
          onAddBoard();
          resetWhiteboard();
          whiteboardStore.activeDrawSelectTool();
        }
        ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_OPEN_RESOURCE_DIALOG, {});
      } catch (e) {
        logger.error("【在线上课模式】", "白板初始化失败", e);
      }
    });
    onUnmounted(() => {
      if (officeViewSdk) {
        officeViewSdk.destroy();
      }
      cancelList.forEach((item) => {
        typeof item === "function" && item();
      });
      stopHeartbeat();
    });
    const OCTOPUS_STATUS = {
      RECORDING: "recording",
      // 录音中
      ACCOMPLISH: "accomplish"
      // 总结完成
    };
    const configStore = useConfigStore();
    const { isOpenAISummary } = storeToRefs(configStore);
    const deviceStore = useDeviceStore();
    const { serialNumber } = storeToRefs(deviceStore);
    const showAIOctopus = ref(false);
    const aiSummarySwitch = ref(SummarySwitchEnum.CLOSE);
    const isOctopusSpin = ref(false);
    const octopusStatus = ref("");
    const summaryStatus = ref("");
    const isOpenSuccess = ref(false);
    const isSummaryLock = ref(false);
    const showImgDom = ref(false);
    const aiClassSummaryInfo = ref(null);
    const aiClassSummaryId = ref("");
    const onRecording = () => {
      octopusStatus.value = OCTOPUS_STATUS.RECORDING;
      ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_SHOW_AI_SUMMARY_MSG, {
        message: "我也在听老师上课，还会总结知识点哦",
        duration: 3e3
      });
    };
    const onAccomplish = () => {
      octopusStatus.value = OCTOPUS_STATUS.ACCOMPLISH;
      ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_SHOW_AI_SUMMARY_MSG, {
        message: "老师我完成学习总结啦！",
        duration: 3e3
      });
    };
    async function ajaxAISummaryStart2() {
      console.log("AI课堂小结111", isSummaryLock.value);
      if (isSummaryLock.value)
        return;
      isSummaryLock.value = true;
      const res = await handleGenerateAISummary(ClassStatusEnum.START_CLASS);
      isOpenSuccess.value = !!res.aiClassSummaryId;
      isSummaryLock.value = false;
      console.log(
        "录音成功且上课上报成功",
        res.isRecording,
        res.aiClassSummaryId,
        res.isRecording && res.aiClassSummaryId
      );
      if (res.isRecording && res.aiClassSummaryId) {
        console.log("录音成功且上课上报成功11111", res);
        isOctopusSpin.value = true;
        onRecording();
      } else {
        console.log("录音成功且上课上报失败", res);
        isOctopusSpin.value = false;
      }
      console.log("AI课堂小结上报结果", res);
    }
    async function ajaxGetClientConfigList() {
      await configStore.getUserConfigList({
        saasUserId: classTeacher.value.saasUserId,
        disciplineCode: classTeacher.value.disciplineCode
      });
      const aiAssistantConfig = configStore.getUserConfig("ai_assistant");
      if (!aiAssistantConfig) {
        return;
      }
      console.log("查询AI总结是否开启", aiAssistantConfig["AI课堂小结"]);
      aiSummarySwitch.value = aiAssistantConfig["AI课堂小结"];
      ajaxAISummaryStart2();
    }
    const initAIOctopus = async () => {
      try {
        showAIOctopus.value = await setAISummary(SetAISummaryEnum.TARGET_SUBJECT);
        if (showAIOctopus.value) {
          await ajaxGetClientConfigList();
          ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_SHOW_AI_SUMMARY_OCTOPUS, {});
        } else {
          ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_CLOSE_AI_SUMMARY_OCTOPUS, {});
        }
      } catch (e) {
        logger.error("【AIOctopus】", "AI小结初始化失败", e);
      }
    };
    watch(isOpenAISummary, async () => {
      console.log("isOpenAISummary", isOpenAISummary.value);
      if (isOpenAISummary.value === "1") {
        console.log("开启录音ajaxAISummaryStart");
        ajaxAISummaryStart2();
      } else {
        isOctopusSpin.value = false;
        console.log("关闭录音");
        await handleGenerateAISummary(ClassStatusEnum.GIVE_UP_SUMMARY);
      }
    });
    watch(isOctopusSpin, () => {
      if (isOctopusSpin.value) {
        ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_START_ROTATE_AISUMMARY_OCTOPUS, {});
      } else {
        ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_STOP_ROTATE_AISUMMARY_OCTOPUS, {});
      }
    }, {
      immediate: true
    });
    async function ajaxGetAISummaryByid() {
      const res = await getAISummaryByid({
        aiClassSummaryId: localStorage.getItem("aiClassSummaryId")
      });
      aiClassSummaryInfo.value = res.data.aiClassSummary;
      logger.info("【ClassRoom】", "获取AI小结详情", aiClassSummaryInfo.value);
    }
    useEventBus(
      EventBusEnum.AI_CLASS_SUMMARY,
      async (data) => {
        console.log("【AIOctopus】", "收到AI总结成功", data);
        if (data.aiClassSummaryId === localStorage.getItem("aiClassSummaryId") || !localStorage.getItem("aiClassSummaryId")) {
          try {
            await endAudioProcessing();
            isOctopusSpin.value = false;
          } catch (error) {
            console.error("结束录音失败", error);
          }
        }
        if (data.aiClassSummaryId !== localStorage.getItem("aiClassSummaryId"))
          return;
        await ajaxGetAISummaryByid();
        await nextTick();
        aiClassSummaryId.value = localStorage.getItem("aiClassSummaryId");
        try {
          await deviceStore.getDeviceSerialNumber();
        } catch (error) {
          logger.warn("【ClassRoom】", "获取设备序列号失败", error);
        }
        if (!aiClassSummaryInfo.value?.snapshotUrl) {
          showImgDom.value = true;
        }
        summaryStatus.value = SummaryTypeEnum.AI_SUMMARIZED;
        ZmqUtils.sendRequestQtCef(CEF_NAME.CLASS_SUMMARY_DIALOG_CEF, CEF_RENDERER_MESSAGE_TYPE.CLASS_SUMMARY_AI_SUMMARY_ACCOMPLISH);
        console.log("【AIOctopus】10S", "收到AI总结成功", summaryStatus.value);
        onAccomplish();
      }
    );
    cancelList.push(ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_GET_AI_SUMMARY_SWITCH, async () => {
      return ZmqMsg.newResponse({
        isOpenAISummary: isOpenAISummary.value
      });
    }));
    cancelList.push(ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_SET_AI_SUMMARY_SWITCH, async (data) => {
      try {
        const _isOpenAISummary = data?.data?.isOpenAISummary;
        await configStore.setUserConfig({
          saasUserId: classTeacher.value.saasUserId,
          disciplineId: classTeacher.value.disciplineId,
          disciplineCode: classTeacher.value.disciplineCode,
          configGroup: "ai_assistant",
          configKey: "AI课堂小结",
          configValue: _isOpenAISummary
        }, {
          isZmqQtShow: true
        });
        return ZmqMsg.newResponse({
          isOpenAISummary: isOpenAISummary.value,
          message: "设置成功"
        });
      } catch (error) {
        logger.error("【AI课堂小结】", "设置AI总结开关失败", error);
        return ZmqMsg.newErrorResponse(error?.message || error || "设置失败");
      }
    }));
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_GET_AI_CLASS_SUMMARY_ID, async () => {
        return ZmqMsg.newResponse({
          aiClassSummaryId: localStorage.getItem("aiClassSummaryId")
        });
      })
    );
    cancelList.push(
      ZmqUtils.registerHandler(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_START_SUMMARY, async () => {
        await handleGenerateAISummary(ClassStatusEnum.FINISH_SUMMARY);
        return ZmqMsg.newResponse({
          message: "触发开始总结成功"
        });
      })
    );
    onMounted(() => {
      initAIOctopus();
    });
    return (_ctx, _cache) => {
      const _component_XMind = __unplugin_components_0;
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createBaseVNode("div", {
          class: normalizeClass(["whiteboard-wrap", `whiteboard-${whiteboardTheme.value}`])
        }, [
          isViewOffice.value ? (openBlock(), createElementBlock("iframe", {
            key: 0,
            ref_key: "officeViewRef",
            ref: officeViewRef,
            class: "classroom-office-view",
            src: officeViewUrl.value
          }, null, 8, _hoisted_2)) : createCommentVNode("", true)
        ], 2),
        isQTConnectError.value ? (openBlock(), createElementBlock("div", _hoisted_3, [
          createBaseVNode("div", {
            class: "qt-connect-error-button",
            onClick: onResetQt
          }, "重试"),
          createBaseVNode("div", {
            class: "qt-connect-error-button",
            onClick: onExitHome
          }, "退出")
        ])) : createCommentVNode("", true),
        showImgDom.value && aiClassSummaryInfo.value?.summary ? (openBlock(), createElementBlock("div", _hoisted_4, [
          createVNode(_component_XMind, {
            ref: "xmindRef",
            key: "jsmind_container_img",
            "xmind-json-data": aiClassSummaryInfo.value?.summary,
            "snapshot-url": aiClassSummaryInfo.value?.snapshotUrl,
            "container-id": "jsmind_container_img",
            "ai-class-summary-id": aiClassSummaryId.value,
            "class-teacher": unref(classTeacher),
            "current-resource": unref(currentResource),
            "serial-number": unref(serialNumber),
            "is-summary-list": false
          }, null, 8, ["xmind-json-data", "snapshot-url", "ai-class-summary-id", "class-teacher", "current-resource", "serial-number"])
        ])) : createCommentVNode("", true)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_0cee25d3_lang = "";
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-0cee25d3"]]);
export {
  index as default
};
