import { B as BridgeZmqUtils, l as logger, _ as _export_sfc, a as Bridge, d as initLoggerWidget, p as pinia } from "./index.8338496f.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
/* empty css                     */import { d as defineComponent, r as ref, z as onMounted, h as withDirectives, b as openBlock, m as createElementBlock, j as createBaseVNode, p as createVNode, v as vShow, e as createBlock, f as withCtx, k as normalizeClass, D as createCommentVNode, ad as createApp } from "./bootstrap.ab073eb8.js";
import { D as Dialog } from "./index.091a2398.js";
import "./base.676dddc3.js";
import { C as CEF_RENDERER_MESSAGE_TYPE, Q as QT_CEF_MESSAGE_TYPE } from "./IComm.3bca3c7b.js";
import { s as showWarning } from "./toastWidget.f897117d.js";
import { E as ElSwitch, v as vLoading } from "./directive.aac8de61.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./index.1c1fd1ce.js";
import "./use-form-common-props.6b0d7cd2.js";
import "./index.4d07c967.js";
import "./event.183fce42.js";
const elSwitch = "";
const _hoisted_1$1 = { class: "container" };
const _hoisted_2$1 = { class: "flex header" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "TeacherAssistantSetting",
  setup(__props) {
    const isOpenAISummary = ref("1");
    const loading = ref(false);
    const loadLoading = ref(true);
    const onChange = async (value) => {
      if (loading.value) {
        showWarning("正在设置，请稍后");
        return;
      }
      try {
        loading.value = true;
        const res = await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_SET_AI_SUMMARY_SWITCH, {
          isOpenAISummary: value
        });
        logger.info("【设置弹窗】", "设置AI课堂小结开关成功", res);
        await getIsOpenAISummary();
      } catch (e) {
        logger.error("【设置弹窗】", "设置AI课堂小结开关失败", e);
      } finally {
        loading.value = false;
      }
    };
    const getIsOpenAISummary = async () => {
      try {
        const res = await BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_GET_AI_SUMMARY_SWITCH);
        if (res) {
          isOpenAISummary.value = res.isOpenAISummary;
        }
        logger.info("【设置弹窗】", "获取AI课堂小结开启状态成功", res);
      } catch (e) {
        logger.error("【设置弹窗】", "获取AI课堂小结开启状态失败", e);
      }
    };
    onMounted(async () => {
      try {
        loadLoading.value = true;
        await getIsOpenAISummary();
      } finally {
        loadLoading.value = false;
      }
    });
    return (_ctx, _cache) => {
      const _component_el_switch = ElSwitch;
      const _directive_loading = vLoading;
      return withDirectives((openBlock(), createElementBlock("div", _hoisted_1$1, [
        withDirectives(createBaseVNode("div", null, [
          createBaseVNode("div", _hoisted_2$1, [
            _cache[0] || (_cache[0] = createBaseVNode("div", null, "AI课堂小结", -1)),
            createBaseVNode("div", null, [
              createVNode(_component_el_switch, {
                "model-value": isOpenAISummary.value,
                "active-value": "1",
                "inactive-value": "0",
                class: "switch",
                style: { "--el-switch-on-color": "#13ce66" },
                onChange
              }, null, 8, ["model-value"])
            ])
          ]),
          _cache[1] || (_cache[1] = createBaseVNode("div", { class: "desc" }, " 启用后，AI将识别分析授课的课件内容，及课堂录音，在课堂结束前5分钟自动生成课堂小结，协助老师带着学生回顾知识点。 ", -1))
        ], 512), [
          [vShow, !loadLoading.value]
        ])
      ])), [
        [_directive_loading, loadLoading.value]
      ]);
    };
  }
});
const TeacherAssistantSetting_vue_vue_type_style_index_0_scoped_68a1b17a_lang = "";
const TeacherAssistantSetting = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-68a1b17a"]]);
const _hoisted_1 = { class: "setting-container flex" };
const _hoisted_2 = { class: "setting-content" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const visible = ref(true);
    const closeDialog = () => {
      Bridge.getInstance().callVoid(QT_CEF_MESSAGE_TYPE.CLOSE);
    };
    return (_ctx, _cache) => {
      return visible.value ? (openBlock(), createBlock(Dialog, {
        key: 0,
        class: "setting-dialog",
        visible: visible.value,
        title: "设置",
        "show-close": "",
        onClose: closeDialog
      }, {
        default: withCtx(() => [
          createBaseVNode("div", _hoisted_1, [
            _cache[0] || (_cache[0] = createBaseVNode("div", { class: "setting-menu flex flex-v" }, [
              createBaseVNode("span", {
                class: normalizeClass({
                  selected: true
                })
              }, "AI教师助手")
            ], -1)),
            createBaseVNode("div", _hoisted_2, [
              createVNode(TeacherAssistantSetting)
            ])
          ])
        ]),
        _: 1
      }, 8, ["visible"])) : createCommentVNode("", true);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_d0500c4c_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-d0500c4c"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
