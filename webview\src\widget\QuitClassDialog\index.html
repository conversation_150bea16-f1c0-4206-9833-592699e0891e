<!doctype html><html><head><meta charset="UTF-8"/><title>星讲台</title><script src="../../../libs/track/index.js"></script><script>window._track = new WebTrack({
      env: 'dev',
      config: {
        logstore: 'hl-black-board-client-log'
      },
      watchEvents: {
        /** 目前全埋点监控仅限web端，wx小程序暂未支持 */
        clickMount: true,
        // 开启全埋点监控
        page: false // openTelemetry的page有更详细性能数据，关闭原指标
      },
      openTelemetry: {
        // 开启openTelemetry
        fetch: true,
        // 接口trace
        xhr: true,
        // 接口trace
        page: true,
        // 页面性能统计trace
        userInteraction: true // 用户行为trace
      },
      globalData: {
        _project_: 'black-board-client',
        _project_name_: '星讲台'
      },
      terminal: 'windows',
      // 自定义过滤
      filterRule: function(data) {
        try {
          var eventCode = data._event_code_;
          var eventInfo = data._event_info_
          switch (eventCode) {
            case 'CLICK':
              if (!eventInfo) {return true}
              if (typeof eventInfo === 'string') {
                eventInfo = JSON.parse(eventInfo);
              }
              // 点击事件，如果text和module都为空，则不发送埋点数据
              var isOuterText = eventInfo.text;
              var isInnerText;
              var isInnerModule;
              if (eventInfo.dataset) {
                isInnerText = eventInfo.dataset.text;
                isInnerModule = eventInfo.dataset.module;
              }
              return !isOuterText && !isInnerText && !isInnerModule;
          }
          return false;
        } catch {
          return false;
        }
      }
    });</script><script type="module" crossorigin src="../../../js/QuitClassDialog.3f43b196.js"></script><link rel="modulepreload" crossorigin href="../../../js/encryptlong.f30353e7.js"><link rel="modulepreload" crossorigin href="../../../js/bootstrap.ab073eb8.js"><link rel="modulepreload" crossorigin href="../../../js/base.649d38c6.js"><link rel="modulepreload" crossorigin href="../../../js/index.8338496f.js"><link rel="modulepreload" crossorigin href="../../../js/rem.5d1b1196.js"><link rel="modulepreload" crossorigin href="../../../js/index.1c1fd1ce.js"><link rel="modulepreload" crossorigin href="../../../js/index.091a2398.js"><link rel="modulepreload" crossorigin href="../../../js/base.676dddc3.js"><link rel="modulepreload" crossorigin href="../../../js/use-form-common-props.6b0d7cd2.js"><link rel="modulepreload" crossorigin href="../../../js/el-button.a9e8e4ae.js"><link rel="modulepreload" crossorigin href="../../../js/IComm.3bca3c7b.js"><link rel="stylesheet" href="../../../css/index.046829b5.css"><link rel="stylesheet" href="../../../css/index.cff54096.css"><link rel="stylesheet" href="../../../css/base.36af3b57.css"><link rel="stylesheet" href="../../../css/el-button.1f189f69.css"><link rel="stylesheet" href="../../../css/index.db29002d.css"></head><body><div id="app"></div><script src="../../../libs/iconfont.js?asset"></script></body></html>