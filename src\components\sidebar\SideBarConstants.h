#ifndef SIDEBARCONSTANTS_H
#define SIDEBARCONSTANTS_H

#include "../../screen_adaptation/ScreenAdaptationManager.h"
#include "../whiteboardview/ZIndexManager.h"
#include <QString>
#include <QMap>
#include <QRect>
#include <QMetaType>
#include <nlohmann/json.hpp>
using json = nlohmann::json;
/**
 * @brief 侧边栏组件常量定义
 * 
 * 定义侧边栏组件的尺寸、样式和动画相关常量
 * 所有尺寸基于4K分辨率设计，通过屏幕适配管理器进行缩放
 */
namespace SideBarConstants {

    // ========== 视图类型枚举 ==========
    /**
     * @brief 侧边栏控件视图类型枚举
     */
    enum class ViewType {
        Normal = 0,         ///< 普通按钮（图标+文字）
        Grid = 1,           ///< 网格按钮（小按钮，一行两个）
        Divide = 2          ///< 分割线
    };

    // ========== 点击类型枚举 ==========
    /**
     * @brief 侧边栏按钮点击类型枚举
     */
    enum class ClickType {
        None = 0,           ///< 无点击类型
        Cef = 1,            ///< CEF相关
        ElectronFunc = 2,   ///< Electron功能
        ToolBox = 3,         ///< 工具箱
        Qt = 4             ///< Qt相关
    };

    // ========== 工具信息结构体 ==========
    /**
     * @brief 侧边栏工具信息
     */
    struct ToolInfo {
        QString toolName;        ///< 工具名称
        QString toolIcon;        ///< 工具图标路径
        ViewType viewType;       ///< 控件类型
        ClickType clickType;     ///< 点击类型
        QString url;             ///< 工具URL
        QRect geometry;          ///< 工具窗口几何信息
        int zlevel;              ///< 层级（直接是int类型）
        bool showToolBox;        ///< 是否显示工具箱
        bool showDragBar;        ///< 是否显示拖拽图标 默认为false
        bool showControlBar;     ///< 是否显示控制栏
        bool fullscreen;         ///< 是否显示全屏
        bool showFullScreenButton; ///< 是否显示全屏按钮

        ToolInfo() : viewType(ViewType::Normal), clickType(ClickType::None), zlevel(0), showToolBox(false), showDragBar(false), showControlBar(false), fullscreen(false), showFullScreenButton(false){}
        ToolInfo(const QString& name, const QString& icon, ViewType vType = ViewType::Normal, ClickType cType = ClickType::None)
            : toolName(name), toolIcon(icon), viewType(vType), clickType(cType), zlevel(0), showToolBox(false), showDragBar(false), showControlBar(false), fullscreen(false), showFullScreenButton(false){}
    };

    // ========== 工具映射定义 ==========
    /**
     * @brief 获取侧边栏工具映射
     * @return 工具名称到工具信息的映射
     */
    QMap<QString, ToolInfo> getToolsMap();

    // ========== 展开状态尺寸常量 ==========
    const int EXPANDED_WIDTH = 140;                 ///< 展开状态宽度
    const int EXPANDED_HEIGHT = 1679;               ///< 展开状态高度
    
    // ========== 收起状态尺寸常量 ==========
    const int COLLAPSED_WIDTH = 18;                  ///< 收起状态视觉宽度
    const int COLLAPSED_HEIGHT = 468;               ///< 收起状态高度
    const int COLLAPSED_RIGHT_MARGIN = 18;          ///< 收起状态距右边距离
    const int COLLAPSED_CLICK_AREA_WIDTH = 40;      ///< 收起状态点击区域宽度
    const int COLLAPSED_CLICK_AREA_EXTRA = 15;      ///< 收起状态点击区域左右扩展
    
    // ========== 样式常量 ==========
    const double BORDER_RADIUS = 9;               ///< 收起状态圆角半径
    const double EXPANDED_LEFT_BORDER_RADIUS = 38.0; ///< 展开状态左侧圆角半径
    const int EXPANDED_BG_ALPHA = 166;              ///< 展开状态背景透明度 (0.65 * 255)
    
    // ========== 动画常量 ==========
    const int ANIMATION_DURATION = 300;             ///< 动画持续时间（毫秒）
    const int BUTTON_SIZE = 24;                     ///< 内部按钮大小
    const int CONTAINER_PADDING = 25;                   ///< 容器内边距
    const int BUTTON_TOP_MARGIN = 40;                   ///< 按钮边距

    // ========== 时间显示常量 ==========
    const int TIME_DISPLAY_FONT_SIZE = 38;          ///< 时间显示字体大小

    // ========== 功能按钮常量 ==========
    const int FUNCTION_BUTTON_SIZE = 66;            ///< 功能按钮图标大小
    const int FUNCTION_BUTTON_TEXT_SIZE = 28;       ///< 功能按钮文字大小
    const int SMALL_BUTTON_SIZE = 40;               ///< 小按钮图标大小
    const int SMALL_BUTTON_SPACING = 20;            ///< 小按钮之间的间距

    // ========== 分割栏常量 ==========
    const int DIVIDER_WIDTH = 112;                  ///< 分割栏宽度

    // ========== 收起按钮常量 ==========
    const int COLLAPSE_BUTTON_VISUAL_WIDTH = 22;    ///< 收起按钮视觉宽度
    const int COLLAPSE_BUTTON_VISUAL_HEIGHT = 18;   ///< 收起按钮视觉高度
    const int COLLAPSE_BUTTON_CLICK_SIZE = 40;      ///< 收起按钮点击区域大小

    // ========== 工具箱弹窗常量 ==========
    const int TOOLBOX_POPUP_WIDTH = 640;            ///< 工具箱弹窗宽度
    const int TOOLBOX_POPUP_HEIGHT = 432;           ///< 工具箱弹窗高度
    const int TOOLBOX_POPUP_BORDER_RADIUS = 40;     ///< 工具箱弹窗圆角半径
    const int TOOLBOX_POPUP_PADDING = 40;           ///< 工具箱弹窗内边距
    const int TOOLBOX_POPUP_SPACING = 20;           ///< 工具箱弹窗与侧边栏的间距
    const int TOOLBOX_POPUP_TITLE_SPACING = 18;     ///< 工具箱弹窗标题与按钮的间距
    
    // ========== 屏幕适配函数 ==========
    
    /**
     * @brief 获取适配后的展开状态宽度
     */
    inline int getExpandedWidth() {
        return ScreenAdaptationConstants::adaptSize(EXPANDED_WIDTH);
    }
    
    /**
     * @brief 获取适配后的展开状态高度
     */
    inline int getExpandedHeight() {
        return ScreenAdaptationConstants::adaptSize(EXPANDED_HEIGHT);
    }
    
    /**
     * @brief 获取适配后的收起状态宽度
     */
    inline int getCollapsedWidth() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSED_WIDTH);
    }
    
    /**
     * @brief 获取适配后的收起状态高度
     */
    inline int getCollapsedHeight() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSED_HEIGHT);
    }
    
    /**
     * @brief 获取适配后的收起状态右边距
     */
    inline int getCollapsedRightMargin() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSED_RIGHT_MARGIN);
    }

    /**
     * @brief 获取适配后的收起状态点击区域宽度
     */
    inline int getCollapsedClickAreaWidth() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSED_CLICK_AREA_WIDTH);
    }

    /**
     * @brief 获取适配后的收起状态点击区域扩展
     */
    inline int getCollapsedClickAreaExtra() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSED_CLICK_AREA_EXTRA);
    }
    
    /**
     * @brief 获取适配后的按钮大小
     */
    inline int getButtonSize() {
        return ScreenAdaptationConstants::adaptSize(BUTTON_SIZE);
    }


    /**
     * @brief 获取适配后的容器顶部内边距
     */
    inline int getContainerPadding() {
        return ScreenAdaptationConstants::adaptSize(CONTAINER_PADDING);
    }
    
    /**
     * @brief 获取适配后的按钮边距
     */
    inline int getButtonTopMargin() {
        return ScreenAdaptationConstants::adaptSize(BUTTON_TOP_MARGIN);
    }
    
    /**
     * @brief 获取适配后的圆角半径（收起状态）
     */
    inline qreal getBorderRadius() {
        return ScreenAdaptationConstants::adaptSize(BORDER_RADIUS);
    }

    /**
     * @brief 获取适配后的展开状态左侧圆角半径
     */
    inline qreal getExpandedLeftBorderRadius() {
        return ScreenAdaptationConstants::adaptSize(EXPANDED_LEFT_BORDER_RADIUS);
    }

    /**
     * @brief 获取适配后的时间显示字体大小
     */
    inline int getTimeDisplayFontSize() {
        return ScreenAdaptationConstants::adaptSize(TIME_DISPLAY_FONT_SIZE);
    }

    /**
     * @brief 获取适配后的功能按钮图标大小
     */
    inline int getFunctionButtonSize() {
        return ScreenAdaptationConstants::adaptSize(FUNCTION_BUTTON_SIZE);
    }

    /**
     * @brief 获取适配后的功能按钮文字大小
     */
    inline int getFunctionButtonTextSize() {
        return ScreenAdaptationConstants::adaptSize(FUNCTION_BUTTON_TEXT_SIZE);
    }

    /**
     * @brief 获取适配后的小按钮图标大小
     */
    inline int getSmallButtonSize() {
        return ScreenAdaptationConstants::adaptSize(SMALL_BUTTON_SIZE);
    }

    /**
     * @brief 获取适配后的小按钮间距
     */
    inline int getSmallButtonSpacing() {
        return ScreenAdaptationConstants::adaptSize(SMALL_BUTTON_SPACING);
    }

    /**
     * @brief 获取适配后的分割栏宽度
     */
    inline int getDividerWidth() {
        return ScreenAdaptationConstants::adaptSize(DIVIDER_WIDTH);
    }

    /**
     * @brief 获取适配后的收起按钮视觉宽度
     */
    inline int getCollapseButtonVisualWidth() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSE_BUTTON_VISUAL_WIDTH);
    }

    /**
     * @brief 获取适配后的收起按钮视觉高度
     */
    inline int getCollapseButtonVisualHeight() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSE_BUTTON_VISUAL_HEIGHT);
    }

    /**
     * @brief 获取适配后的收起按钮点击区域大小
     */
    inline int getCollapseButtonClickSize() {
        return ScreenAdaptationConstants::adaptSize(COLLAPSE_BUTTON_CLICK_SIZE);
    }

    /**
     * @brief 获取适配后的工具箱弹窗宽度
     */
    inline int getToolboxPopupWidth() {
        return ScreenAdaptationConstants::adaptSize(TOOLBOX_POPUP_WIDTH);
    }

    /**
     * @brief 获取适配后的工具箱弹窗高度
     */
    inline int getToolboxPopupHeight() {
        return ScreenAdaptationConstants::adaptSize(TOOLBOX_POPUP_HEIGHT);
    }

    /**
     * @brief 获取适配后的工具箱弹窗圆角半径
     */
    inline int getToolboxPopupBorderRadius() {
        return ScreenAdaptationConstants::adaptSize(TOOLBOX_POPUP_BORDER_RADIUS);
    }

    /**
     * @brief 获取适配后的工具箱弹窗内边距
     */
    inline int getToolboxPopupPadding() {
        return ScreenAdaptationConstants::adaptSize(TOOLBOX_POPUP_PADDING);
    }

    /**
     * @brief 获取适配后的工具箱弹窗间距
     */
    inline int getToolboxPopupSpacing() {
        return ScreenAdaptationConstants::adaptSize(TOOLBOX_POPUP_SPACING);
    }

    /**
     * @brief 获取适配后的工具箱弹窗标题间距
     */
    inline int getToolboxPopupTitleSpacing() {
        return ScreenAdaptationConstants::adaptSize(TOOLBOX_POPUP_TITLE_SPACING);
    }
}

// 注册ToolInfo类型以便在QVariant中使用
Q_DECLARE_METATYPE(SideBarConstants::ToolInfo)

#endif // SIDEBARCONSTANTS_H
