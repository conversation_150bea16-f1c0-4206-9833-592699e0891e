#ifndef ICONFONTMANAGER_H
#define ICONFONTMANAGER_H

#include <QObject>
#include <QFont>
#include <QFontDatabase>
#include <QIcon>
#include <QPixmap>
#include <QPainter>
#include <QColor>
#include <QSize>
#include <QHash>
#include <QString>

/**
 * @brief IconFont管理器类
 *
 * 负责管理iconfont字体的加载、缓存和图标生成
 * 支持动态颜色、大小调整和图标缓存机制
 */
class IconFontManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return IconFontManager实例指针
     */
    static IconFontManager* instance();

    /**
     * @brief 析构函数
     */
    ~IconFontManager();

    /**
     * @brief 加载字体文件
     * @param fontPath 字体文件路径
     * @param fontFamily 字体族名称（可选，如果为空则自动检测）
     * @return 是否加载成功
     */
    bool loadFont(const QString& fontPath, const QString& fontFamily = QString());

    /**
     * @brief 从资源文件加载默认iconfont字体
     * @return 是否加载成功
     */
    bool loadDefaultIconFont();

    /**
     * @brief 设置默认字体族
     * @param fontFamily 字体族名称
     */
    void setDefaultFontFamily(const QString& fontFamily);

    /**
     * @brief 获取当前字体族
     * @return 字体族名称
     */
    QString currentFontFamily() const;

    /**
     * @brief 生成图标
     * @param unicode Unicode字符码点（如：0xe001）
     * @param size 图标大小
     * @param color 图标颜色
     * @param fontFamily 字体族名称（可选，使用默认字体族）
     * @return QIcon对象
     */
    QIcon createIcon(int unicode, const QSize& size = QSize(16, 16),
                     const QColor& color = QColor(Qt::black),
                     const QString& fontFamily = QString());

    /**
     * @brief 生成图标（使用字符串Unicode）
     * @param unicodeStr Unicode字符串（如："\\ue001"）
     * @param size 图标大小
     * @param color 图标颜色
     * @param fontFamily 字体族名称（可选）
     * @return QIcon对象
     */
    QIcon createIcon(const QString& unicodeStr, const QSize& size = QSize(16, 16),
                     const QColor& color = QColor(Qt::black),
                     const QString& fontFamily = QString());

    /**
     * @brief 生成像素图
     * @param unicode Unicode字符码点
     * @param size 图标大小
     * @param color 图标颜色
     * @param fontFamily 字体族名称（可选）
     * @return QPixmap对象
     */
    QPixmap createPixmap(int unicode, const QSize& size = QSize(16, 16),
                         const QColor& color = QColor(Qt::black),
                         const QString& fontFamily = QString());

    /**
     * @brief 获取字体对象
     * @param fontSize 字体大小
     * @param fontFamily 字体族名称（可选）
     * @return QFont对象
     */
    QFont getFont(int fontSize = 16, const QString& fontFamily = QString());

    /**
     * @brief 清除图标缓存
     */
    void clearCache();

    /**
     * @brief 设置是否启用缓存
     * @param enabled 是否启用
     */
    void setCacheEnabled(bool enabled);

    /**
     * @brief 获取已加载的字体族列表
     * @return 字体族名称列表
     */
    QStringList getLoadedFontFamilies() const;

    /**
     * @brief 检查字体是否已加载
     * @param fontFamily 字体族名称
     * @return 是否已加载
     */
    bool isFontLoaded(const QString& fontFamily) const;

    /**
     * @brief Unicode字符串转整数
     * @param unicodeStr Unicode字符串（如："\\ue001" 或 "e001"）
     * @return Unicode码点
     */
    static int unicodeStringToInt(const QString& unicodeStr);

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    explicit IconFontManager(QObject *parent = nullptr);

    /**
     * @brief 生成缓存键
     * @param unicode Unicode码点
     * @param size 大小
     * @param color 颜色
     * @param fontFamily 字体族
     * @return 缓存键
     */
    QString generateCacheKey(int unicode, const QSize& size,
                           const QColor& color, const QString& fontFamily) const;

    /**
     * @brief 内部创建像素图方法
     * @param unicode Unicode码点
     * @param size 大小
     * @param color 颜色
     * @param fontFamily 字体族
     * @return QPixmap对象
     */
    QPixmap createPixmapInternal(int unicode, const QSize& size,
                               const QColor& color, const QString& fontFamily);

private:
    static IconFontManager* s_instance;     ///< 单例实例
    QString m_defaultFontFamily;            ///< 默认字体族
    QStringList m_loadedFontFamilies;       ///< 已加载的字体族列表
    QHash<QString, QPixmap> m_iconCache;    ///< 图标缓存
    bool m_cacheEnabled;                    ///< 是否启用缓存
    QFontDatabase m_fontDatabase;           ///< 字体数据库
};

#endif // ICONFONTMANAGER_H
