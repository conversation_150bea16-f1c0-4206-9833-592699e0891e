import { B as BridgeZmqUtils, c as setBusinessInfoWidget, l as logger, a as Bridge, _ as _export_sfc, d as initLoggerWidget, p as pinia } from "./index.8338496f.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { b as openBlock, m as createElementBlock, F as Fragment, R as renderList, k as normalizeClass, l as normalizeStyle, I as withModifiers, B as renderSlot, d as defineComponent, s as shallowRef, r as ref, c as computed, w as watch, z as onMounted, D as createCommentVNode, e as createBlock, f as withCtx, j as createBaseVNode, p as createVNode, G as createTextVNode, H as toDisplayString, u as unref, n as nextTick, ad as createApp } from "./bootstrap.ab073eb8.js";
import { l as loadImage } from "./image.77fe4778.js";
import { S as SvgIcon } from "./index.d5831e92.js";
import { g as getDevicePixelRatio } from "./hlwhiteboard.b54f17ff.js";
import { C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.3bca3c7b.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
const style = "";
function D(t) {
  return typeof t == "function" || Object.prototype.toString.call(t) === "[object Function]";
}
function w(t, e, i, s = 1) {
  const [h, o] = typeof s == "number" ? [s, s] : s, a = Math.round(e / h / t[0]) * t[0], l = Math.round(i / o / t[1]) * t[1];
  return [a, l];
}
function R(t, e, i) {
  return t - e - i;
}
function S(t, e, i) {
  return t - e - i;
}
function c(t, e, i) {
  return e !== null && t < e ? e : i !== null && i < t ? i : t;
}
function W(t, e, i) {
  let s = t;
  const h = [
    "matches",
    "webkitMatchesSelector",
    "mozMatchesSelector",
    "msMatchesSelector",
    "oMatchesSelector"
  ].find((o) => D(s[o]));
  if (!D(s[h]))
    return false;
  do {
    if (s[h](e))
      return true;
    if (s === i)
      return false;
    s = s.parentNode;
  } while (s);
  return false;
}
function N(t) {
  const e = window.getComputedStyle(t);
  return [
    parseFloat(e.getPropertyValue("width"), 10),
    parseFloat(e.getPropertyValue("height"), 10)
  ];
}
function b(t, e, i) {
  t && (t.attachEvent ? t.attachEvent("on" + e, i) : t.addEventListener ? t.addEventListener(e, i, true) : t["on" + e] = i);
}
function f(t, e, i) {
  t && (t.detachEvent ? t.detachEvent("on" + e, i) : t.removeEventListener ? t.removeEventListener(e, i, true) : t["on" + e] = null);
}
const P = (t, e) => {
  const i = t.__vccOpts || t;
  for (const [s, h] of e)
    i[s] = h;
  return i;
}, y = {
  mouse: {
    start: "mousedown",
    move: "mousemove",
    stop: "mouseup"
  },
  touch: {
    start: "touchstart",
    move: "touchmove",
    stop: "touchend"
  }
}, C = {
  userSelect: "none",
  MozUserSelect: "none",
  WebkitUserSelect: "none",
  MsUserSelect: "none"
}, X = {
  userSelect: "auto",
  MozUserSelect: "auto",
  WebkitUserSelect: "auto",
  MsUserSelect: "auto"
};
let p = y.mouse;
const Y = {
  replace: true,
  name: "vue-draggable-resizable",
  props: {
    className: {
      type: String,
      default: "vdr"
    },
    classNameDraggable: {
      type: String,
      default: "draggable"
    },
    classNameResizable: {
      type: String,
      default: "resizable"
    },
    classNameDragging: {
      type: String,
      default: "dragging"
    },
    classNameResizing: {
      type: String,
      default: "resizing"
    },
    classNameActive: {
      type: String,
      default: "active"
    },
    classNameHandle: {
      type: String,
      default: "handle"
    },
    disableUserSelect: {
      type: Boolean,
      default: true
    },
    enableNativeDrag: {
      type: Boolean,
      default: false
    },
    preventDeactivation: {
      type: Boolean,
      default: false
    },
    active: {
      type: Boolean,
      default: false
    },
    draggable: {
      type: Boolean,
      default: true
    },
    resizable: {
      type: Boolean,
      default: true
    },
    lockAspectRatio: {
      type: Boolean,
      default: false
    },
    w: {
      type: [Number, String],
      default: 200,
      validator: (t) => typeof t == "number" ? t > 0 : t === "auto"
    },
    h: {
      type: [Number, String],
      default: 200,
      validator: (t) => typeof t == "number" ? t > 0 : t === "auto"
    },
    minWidth: {
      type: Number,
      default: 0,
      validator: (t) => t >= 0
    },
    minHeight: {
      type: Number,
      default: 0,
      validator: (t) => t >= 0
    },
    maxWidth: {
      type: Number,
      default: null,
      validator: (t) => t >= 0
    },
    maxHeight: {
      type: Number,
      default: null,
      validator: (t) => t >= 0
    },
    x: {
      type: Number,
      default: 0
    },
    y: {
      type: Number,
      default: 0
    },
    z: {
      type: [String, Number],
      default: "auto",
      validator: (t) => typeof t == "string" ? t === "auto" : t >= 0
    },
    handles: {
      type: Array,
      default: () => ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"],
      validator: (t) => {
        const e = /* @__PURE__ */ new Set(["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"]);
        return new Set(t.filter((i) => e.has(i))).size === t.length;
      }
    },
    dragHandle: {
      type: String,
      default: null
    },
    dragCancel: {
      type: String,
      default: null
    },
    axis: {
      type: String,
      default: "both",
      validator: (t) => ["x", "y", "both"].includes(t)
    },
    grid: {
      type: Array,
      default: () => [1, 1]
    },
    parent: {
      type: Boolean,
      default: false
    },
    scale: {
      type: [Number, Array],
      default: 1,
      validator: (t) => typeof t == "number" ? t > 0 : t.length === 2 && t[0] > 0 && t[1] > 0
    },
    onDragStart: {
      type: Function,
      default: () => true
    },
    onDrag: {
      type: Function,
      default: () => true
    },
    onResizeStart: {
      type: Function,
      default: () => true
    },
    onResize: {
      type: Function,
      default: () => true
    }
  },
  data: function() {
    return {
      left: this.x,
      top: this.y,
      right: null,
      bottom: null,
      width: null,
      height: null,
      widthTouched: false,
      heightTouched: false,
      aspectFactor: null,
      parentWidth: null,
      parentHeight: null,
      handle: null,
      enabled: this.active,
      resizing: false,
      dragging: false,
      dragEnable: false,
      resizeEnable: false,
      zIndex: this.z
    };
  },
  created: function() {
    this.maxWidth && this.minWidth > this.maxWidth && console.warn("[Vdr warn]: Invalid prop: minWidth cannot be greater than maxWidth"), this.maxHeight && this.minHeight > this.maxHeight && console.warn("[Vdr warn]: Invalid prop: minHeight cannot be greater than maxHeight"), this.resetBoundsAndMouseState();
  },
  mounted: function() {
    this.enableNativeDrag || (this.$el.ondragstart = () => false);
    const [t, e] = this.getParentSize();
    this.parentWidth = t, this.parentHeight = e;
    const [i, s] = N(this.$el);
    this.aspectFactor = (this.w !== "auto" ? this.w : i) / (this.h !== "auto" ? this.h : s), this.width = this.w !== "auto" ? this.w : i, this.height = this.h !== "auto" ? this.h : s, this.right = this.parentWidth - this.width - this.left, this.bottom = this.parentHeight - this.height - this.top, this.active && this.$emit("activated"), b(document.documentElement, "mousedown", this.deselect), b(document.documentElement, "touchend touchcancel", this.deselect), b(window, "resize", this.checkParentSize);
  },
  beforeUnmount: function() {
    f(document.documentElement, "mousedown", this.deselect), f(document.documentElement, "touchstart", this.handleUp), f(document.documentElement, "mousemove", this.move), f(document.documentElement, "touchmove", this.move), f(document.documentElement, "mouseup", this.handleUp), f(document.documentElement, "touchend touchcancel", this.deselect), f(window, "resize", this.checkParentSize);
  },
  methods: {
    resetBoundsAndMouseState() {
      this.mouseClickPosition = { mouseX: 0, mouseY: 0, x: 0, y: 0, w: 0, h: 0 }, this.bounds = {
        minLeft: null,
        maxLeft: null,
        minRight: null,
        maxRight: null,
        minTop: null,
        maxTop: null,
        minBottom: null,
        maxBottom: null
      };
    },
    checkParentSize() {
      if (this.parent) {
        const [t, e] = this.getParentSize();
        this.parentWidth = t, this.parentHeight = e, this.right = this.parentWidth - this.width - this.left, this.bottom = this.parentHeight - this.height - this.top;
      }
    },
    getParentSize() {
      if (this.parent) {
        const t = window.getComputedStyle(this.$el.parentNode, null);
        return [
          parseInt(t.getPropertyValue("width"), 10),
          parseInt(t.getPropertyValue("height"), 10)
        ];
      }
      return [null, null];
    },
    elementTouchDown(t) {
      p = y.touch, this.elementDown(t);
    },
    elementMouseDown(t) {
      p = y.mouse, this.elementDown(t);
    },
    elementDown(t) {
      if (t instanceof MouseEvent && t.button !== 0)
        return;
      const e = t.target || t.srcElement;
      if (this.$el.contains(e)) {
        if (this.onDragStart(t) === false)
          return;
        if (this.dragHandle && !W(e, this.dragHandle, this.$el) || this.dragCancel && W(e, this.dragCancel, this.$el)) {
          this.dragging = false;
          return;
        }
        this.enabled || (this.enabled = true, this.$emit("activated"), this.$emit("update:active", true)), this.draggable && (this.dragEnable = true), this.mouseClickPosition.mouseX = t.touches ? t.touches[0].pageX : t.pageX, this.mouseClickPosition.mouseY = t.touches ? t.touches[0].pageY : t.pageY, this.mouseClickPosition.left = this.left, this.mouseClickPosition.right = this.right, this.mouseClickPosition.top = this.top, this.mouseClickPosition.bottom = this.bottom, this.parent && (this.bounds = this.calcDragLimits()), b(document.documentElement, p.move, this.move), b(document.documentElement, p.stop, this.handleUp);
      }
    },
    calcDragLimits() {
      return {
        minLeft: this.left % this.grid[0],
        maxLeft: Math.floor((this.parentWidth - this.width - this.left) / this.grid[0]) * this.grid[0] + this.left,
        minRight: this.right % this.grid[0],
        maxRight: Math.floor((this.parentWidth - this.width - this.right) / this.grid[0]) * this.grid[0] + this.right,
        minTop: this.top % this.grid[1],
        maxTop: Math.floor((this.parentHeight - this.height - this.top) / this.grid[1]) * this.grid[1] + this.top,
        minBottom: this.bottom % this.grid[1],
        maxBottom: Math.floor((this.parentHeight - this.height - this.bottom) / this.grid[1]) * this.grid[1] + this.bottom
      };
    },
    deselect(t) {
      const e = t.target || t.srcElement, i = new RegExp(this.className + "-([trmbl]{2})", "");
      !this.$el.contains(e) && !i.test(e.className) && (this.enabled && !this.preventDeactivation && (this.enabled = false, this.$emit("deactivated"), this.$emit("update:active", false)), f(document.documentElement, p.move, this.handleResize)), this.resetBoundsAndMouseState();
    },
    handleTouchDown(t, e) {
      p = y.touch, this.handleDown(t, e);
    },
    handleDown(t, e) {
      e instanceof MouseEvent && e.which !== 1 || this.onResizeStart(t, e) !== false && (e.stopPropagation && e.stopPropagation(), this.lockAspectRatio && !t.includes("m") ? this.handle = "m" + t.substring(1) : this.handle = t, this.resizeEnable = true, this.mouseClickPosition.mouseX = e.touches ? e.touches[0].pageX : e.pageX, this.mouseClickPosition.mouseY = e.touches ? e.touches[0].pageY : e.pageY, this.mouseClickPosition.left = this.left, this.mouseClickPosition.right = this.right, this.mouseClickPosition.top = this.top, this.mouseClickPosition.bottom = this.bottom, this.bounds = this.calcResizeLimits(), b(document.documentElement, p.move, this.handleResize), b(document.documentElement, p.stop, this.handleUp));
    },
    calcResizeLimits() {
      let t = this.minW, e = this.minH, i = this.maxW, s = this.maxH;
      const h = this.aspectFactor, [o, a] = this.grid, l = this.width, g = this.height, r = this.left, u = this.top, m = this.right, d = this.bottom;
      this.lockAspectRatio && (t / e > h ? e = t / h : t = h * e, i && s ? (i = Math.min(i, h * s), s = Math.min(s, i / h)) : i ? s = i / h : s && (i = h * s)), i = i - i % o, s = s - s % a;
      const n = {
        minLeft: null,
        maxLeft: null,
        minTop: null,
        maxTop: null,
        minRight: null,
        maxRight: null,
        minBottom: null,
        maxBottom: null
      };
      return this.parent ? (n.minLeft = r % o, n.maxLeft = r + Math.floor((l - t) / o) * o, n.minTop = u % a, n.maxTop = u + Math.floor((g - e) / a) * a, n.minRight = m % o, n.maxRight = m + Math.floor((l - t) / o) * o, n.minBottom = d % a, n.maxBottom = d + Math.floor((g - e) / a) * a, i && (n.minLeft = Math.max(n.minLeft, this.parentWidth - m - i), n.minRight = Math.max(n.minRight, this.parentWidth - r - i)), s && (n.minTop = Math.max(n.minTop, this.parentHeight - d - s), n.minBottom = Math.max(n.minBottom, this.parentHeight - u - s)), this.lockAspectRatio && (n.minLeft = Math.max(n.minLeft, r - u * h), n.minTop = Math.max(n.minTop, u - r / h), n.minRight = Math.max(n.minRight, m - d * h), n.minBottom = Math.max(n.minBottom, d - m / h))) : (n.minLeft = null, n.maxLeft = r + Math.floor((l - t) / o) * o, n.minTop = null, n.maxTop = u + Math.floor((g - e) / a) * a, n.minRight = null, n.maxRight = m + Math.floor((l - t) / o) * o, n.minBottom = null, n.maxBottom = d + Math.floor((g - e) / a) * a, i && (n.minLeft = -(m + i), n.minRight = -(r + i)), s && (n.minTop = -(d + s), n.minBottom = -(u + s)), this.lockAspectRatio && i && s && (n.minLeft = Math.min(n.minLeft, -(m + i)), n.minTop = Math.min(n.minTop, -(s + d)), n.minRight = Math.min(n.minRight, -r - i), n.minBottom = Math.min(n.minBottom, -u - s))), n;
    },
    move(t) {
      this.resizing ? this.handleResize(t) : this.dragEnable && this.handleDrag(t);
    },
    handleDrag(t) {
      const e = this.axis, i = this.grid, s = this.bounds, h = this.mouseClickPosition, o = e && e !== "y" ? h.mouseX - (t.touches ? t.touches[0].pageX : t.pageX) : 0, a = e && e !== "x" ? h.mouseY - (t.touches ? t.touches[0].pageY : t.pageY) : 0, [l, g] = w(i, o, a, this.scale), r = c(h.left - l, s.minLeft, s.maxLeft), u = c(h.top - g, s.minTop, s.maxTop);
      if (this.onDrag(r, u) === false)
        return;
      const m = c(h.right + l, s.minRight, s.maxRight), d = c(h.bottom + g, s.minBottom, s.maxBottom);
      this.left = r, this.top = u, this.right = m, this.bottom = d, this.$emit("dragging", this.left, this.top), this.dragging = true;
    },
    moveHorizontally(t) {
      const [e, i] = w(this.grid, t, this.top, 1), s = c(e, this.bounds.minLeft, this.bounds.maxLeft);
      this.left = s, this.right = this.parentWidth - this.width - s;
    },
    moveVertically(t) {
      const [e, i] = w(this.grid, this.left, t, 1), s = c(i, this.bounds.minTop, this.bounds.maxTop);
      this.top = s, this.bottom = this.parentHeight - this.height - s;
    },
    handleResize(t) {
      let e = this.left, i = this.top, s = this.right, h = this.bottom;
      const o = this.mouseClickPosition, a = this.aspectFactor, l = o.mouseX - (t.touches ? t.touches[0].pageX : t.pageX), g = o.mouseY - (t.touches ? t.touches[0].pageY : t.pageY);
      !this.widthTouched && l && (this.widthTouched = true), !this.heightTouched && g && (this.heightTouched = true);
      const [r, u] = w(this.grid, l, g, this.scale);
      this.handle.includes("b") ? (h = c(
        o.bottom + u,
        this.bounds.minBottom,
        this.bounds.maxBottom
      ), this.lockAspectRatio && this.resizingOnY && (s = this.right - (this.bottom - h) * a)) : this.handle.includes("t") && (i = c(
        o.top - u,
        this.bounds.minTop,
        this.bounds.maxTop
      ), this.lockAspectRatio && this.resizingOnY && (e = this.left - (this.top - i) * a)), this.handle.includes("r") ? (s = c(
        o.right + r,
        this.bounds.minRight,
        this.bounds.maxRight
      ), this.lockAspectRatio && this.resizingOnX && (h = this.bottom - (this.right - s) / a)) : this.handle.includes("l") && (e = c(
        o.left - r,
        this.bounds.minLeft,
        this.bounds.maxLeft
      ), this.lockAspectRatio && this.resizingOnX && (i = this.top - (this.left - e) / a));
      const m = R(this.parentWidth, e, s), d = S(this.parentHeight, i, h);
      this.onResize(this.handle, e, i, m, d) !== false && (this.left = e, this.top = i, this.right = s, this.bottom = h, this.width = m, this.height = d, this.$emit("resizing", this.left, this.top, this.width, this.height), this.resizing = true);
    },
    changeWidth(t) {
      const [e, i] = w(this.grid, t, 0, 1), s = c(
        this.parentWidth - e - this.left,
        this.bounds.minRight,
        this.bounds.maxRight
      );
      let h = this.bottom;
      this.lockAspectRatio && (h = this.bottom - (this.right - s) / this.aspectFactor);
      const o = R(this.parentWidth, this.left, s), a = S(this.parentHeight, this.top, h);
      this.right = s, this.bottom = h, this.width = o, this.height = a;
    },
    changeHeight(t) {
      const [e, i] = w(this.grid, 0, t, 1), s = c(
        this.parentHeight - i - this.top,
        this.bounds.minBottom,
        this.bounds.maxBottom
      );
      let h = this.right;
      this.lockAspectRatio && (h = this.right - (this.bottom - s) * this.aspectFactor);
      const o = R(this.parentWidth, this.left, h), a = S(this.parentHeight, this.top, s);
      this.right = h, this.bottom = s, this.width = o, this.height = a;
    },
    handleUp(t) {
      this.handle = null, this.resetBoundsAndMouseState(), this.dragEnable = false, this.resizeEnable = false, this.resizing && (this.resizing = false, this.$emit("resizeStop", this.left, this.top, this.width, this.height)), this.dragging && (this.dragging = false, this.$emit("dragStop", this.left, this.top)), f(document.documentElement, p.move, this.handleResize);
    }
  },
  computed: {
    style() {
      return {
        transform: `translate(${this.left}px, ${this.top}px)`,
        width: this.computedWidth,
        height: this.computedHeight,
        zIndex: this.zIndex,
        ...this.dragging && this.disableUserSelect ? C : X
      };
    },
    actualHandles() {
      return this.resizable ? this.handles : [];
    },
    computedWidth() {
      return this.w === "auto" && !this.widthTouched ? "auto" : this.width + "px";
    },
    computedHeight() {
      return this.h === "auto" && !this.heightTouched ? "auto" : this.height + "px";
    },
    minW() {
      return this.minWidth;
    },
    minH() {
      return this.minHeight;
    },
    maxW() {
      return this.maxWidth;
    },
    maxH() {
      return this.maxHeight;
    },
    resizingOnX() {
      return !!this.handle && (this.handle.includes("l") || this.handle.includes("r"));
    },
    resizingOnY() {
      return !!this.handle && (this.handle.includes("t") || this.handle.includes("b"));
    },
    isCornerHandle() {
      return !!this.handle && ["tl", "tr", "br", "bl"].includes(this.handle);
    }
  },
  watch: {
    active(t) {
      this.enabled = t, t ? this.$emit("activated") : this.$emit("deactivated");
    },
    z(t) {
      (t >= 0 || t === "auto") && (this.zIndex = t);
    },
    x(t) {
      this.resizing || this.dragging || (this.parent && (this.bounds = this.calcDragLimits()), this.moveHorizontally(t));
    },
    y(t) {
      this.resizing || this.dragging || (this.parent && (this.bounds = this.calcDragLimits()), this.moveVertically(t));
    },
    lockAspectRatio(t) {
      t ? this.aspectFactor = this.width / this.height : this.aspectFactor = void 0;
    },
    w(t) {
      this.resizing || this.dragging || (this.parent && (this.bounds = this.calcResizeLimits()), this.changeWidth(t));
    },
    h(t) {
      this.resizing || this.dragging || (this.parent && (this.bounds = this.calcResizeLimits()), this.changeHeight(t));
    }
  }
}, A = ["onMousedown", "onTouchstart"];
function F(t, e, i, s, h, o) {
  return openBlock(), createElementBlock("div", {
    style: normalizeStyle(o.style),
    class: normalizeClass([{
      [i.classNameActive]: t.enabled,
      [i.classNameDragging]: t.dragging,
      [i.classNameResizing]: t.resizing,
      [i.classNameDraggable]: i.draggable,
      [i.classNameResizable]: i.resizable
    }, i.className]),
    onMousedown: e[0] || (e[0] = (...a) => o.elementMouseDown && o.elementMouseDown(...a)),
    onTouchstart: e[1] || (e[1] = (...a) => o.elementTouchDown && o.elementTouchDown(...a))
  }, [
    (openBlock(true), createElementBlock(Fragment, null, renderList(o.actualHandles, (a) => (openBlock(), createElementBlock("div", {
      key: a,
      class: normalizeClass([i.classNameHandle, i.classNameHandle + "-" + a]),
      style: normalizeStyle({ display: t.enabled ? "block" : "none" }),
      onMousedown: withModifiers((l) => o.handleDown(a, l), ["stop", "prevent"]),
      onTouchstart: withModifiers((l) => o.handleTouchDown(a, l), ["stop", "prevent"])
    }, [
      renderSlot(t.$slots, a)
    ], 46, A))), 128)),
    renderSlot(t.$slots, "default")
  ], 38);
}
const U = /* @__PURE__ */ P(Y, [["render", F]]);
function M(t) {
  M.installed || (M.installed = true, t.component("VueDraggableResizable", U));
}
const V = {
  install: M
};
let x = null;
typeof window < "u" ? x = window.Vue : typeof global < "u" && (x = global.Vue);
x && x.use(V);
const sleep = (time) => new Promise((resolve) => setTimeout(resolve, time));
const _hoisted_1 = { class: "magnifier" };
const _hoisted_2 = ["src"];
const _hoisted_3 = {
  key: 1,
  class: "magnifier-mask"
};
const _hoisted_4 = { class: "magnifier-toolbar flex flex-jc" };
const amplifySize = 1.2;
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const canvasCopy = document.createElement("canvas");
    const canvasRef = shallowRef();
    const imageUrl = ref();
    const scale = ref(1);
    const showCroppingBox = ref(false);
    const showMask = ref(false);
    const isTheLightOn = ref(true);
    const minWidth = computed(() => {
      return 100 * scale.value;
    });
    const minHeight = computed(() => {
      return 100 * scale.value;
    });
    const resize = ref({
      x: 1161 * scale.value,
      y: 330 * scale.value,
      width: 413 * scale.value,
      height: 413 * scale.value
    });
    const devicePixelRatio = ref(getDevicePixelRatio());
    const initCanvasCopy = () => {
      const { clientWidth, clientHeight } = document.body;
      const _devicePixelRatio = devicePixelRatio.value;
      canvasCopy.width = clientWidth * _devicePixelRatio;
      canvasCopy.height = clientHeight * _devicePixelRatio;
      canvasCopy.style.width = clientWidth + "px";
      canvasCopy.style.height = clientHeight + "px";
    };
    initCanvasCopy();
    const reset = () => {
      if (imageUrl.value) {
        URL.revokeObjectURL(imageUrl.value);
        imageUrl.value = null;
      }
      const ctx = canvasCopy.getContext("2d");
      if (ctx) {
        ctx.clearRect(0, 0, canvasCopy.width, canvasCopy.height);
      }
      showCroppingBox.value = false;
      showMask.value = false;
    };
    const loadScreenshot = async () => {
      const rightToolbarEl = document.querySelector("#rightToolbar");
      const whiteboardToolbarsEl = document.querySelector("#whiteboardToolbars");
      try {
        if (rightToolbarEl) {
          rightToolbarEl.computedStyleMap().get("display");
          rightToolbarEl.style.display = "none";
        }
        if (whiteboardToolbarsEl) {
          whiteboardToolbarsEl.style.display = "none";
        }
        reset();
        const data = await Bridge.getInstance().call("captureScreen");
        imageUrl.value = data.snapshotPath;
        const image = await loadImage(data.snapshotPath);
        const ctx = canvasCopy.getContext("2d");
        if (ctx) {
          const width = canvasCopy.width;
          const height = canvasCopy.height;
          ctx.drawImage(image, 0, 0, width, height, 0, 0, width, height);
        }
        showCroppingBox.value = true;
        showMask.value = true;
        nextTick(() => {
          croppingScreenshot();
        });
      } catch (e) {
        logger.error("【放大镜】", "截屏失败", e);
      } finally {
        if (rightToolbarEl) {
          rightToolbarEl.removeAttribute("style");
        }
        if (whiteboardToolbarsEl) {
          whiteboardToolbarsEl.removeAttribute("style");
        }
      }
    };
    const croppingScreenshot = async () => {
      const canvas = canvasRef.value;
      if (!canvas) {
        logger.warn("【放大镜】", "canvas未初始化");
        return;
      }
      const ctx = canvas.getContext("2d");
      const _devicePixelRatio = devicePixelRatio.value;
      let { width, height, x: x2, y: y2 } = resize.value;
      canvas.style.width = width + "px";
      canvas.style.height = height + "px";
      x2 = x2 * _devicePixelRatio;
      y2 = y2 * _devicePixelRatio;
      width = width * _devicePixelRatio;
      height = height * _devicePixelRatio;
      canvas.width = width;
      canvas.height = height;
      const scaledWidth = width * amplifySize;
      const scaledHeight = height * amplifySize;
      const scaledX = (scaledWidth - width) / 2;
      const scaledY = (scaledHeight - height) / 2;
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(canvasCopy, x2, y2, width, height, -scaledX, -scaledY, scaledWidth, scaledHeight);
      }
    };
    watch(resize, () => {
      requestAnimationFrame(croppingScreenshot);
    });
    const open = async () => {
      const bridge = Bridge.getInstance();
      bridge.call("hideAllToolbars");
      await sleep(50);
      await loadScreenshot();
    };
    const onResizeing = (left, top, width, height) => {
      resize.value = {
        x: left,
        y: top,
        width,
        height
      };
    };
    const onDragging = (left, top) => {
      resize.value = {
        x: left,
        y: top,
        width: resize.value.width,
        height: resize.value.height
      };
    };
    const onClickClose = () => {
      try {
        reset();
      } finally {
        const bridge = Bridge.getInstance();
        bridge.call("showAllToolbars");
        bridge.callVoid("close");
      }
    };
    onMounted(() => {
      open();
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher.saasSchoolId,
          campusId: classTeacher.saasCampusId,
          classId: classTeacher.saasClassId,
          className: classTeacher.saasClassName,
          subjectCode: classTeacher.saasSubjectCode,
          subjectName: classTeacher.saasSubjectName,
          userId: classTeacher.saasUserId
        });
      }).catch((e) => {
        logger.error("【放大镜】", "获取当前上课信息失败", e);
      });
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        imageUrl.value ? (openBlock(), createElementBlock("img", {
          key: 0,
          class: "magnifier-screenshot",
          src: imageUrl.value
        }, null, 8, _hoisted_2)) : createCommentVNode("", true),
        showMask.value && !isTheLightOn.value ? (openBlock(), createElementBlock("div", _hoisted_3)) : createCommentVNode("", true),
        showCroppingBox.value ? (openBlock(), createBlock(unref(U), {
          key: 2,
          x: resize.value.x,
          y: resize.value.y,
          style: normalizeStyle({
            left: `${resize.value.x}px`,
            top: `${resize.value.y}px`
          }),
          class: "magnifier-drag window permanent",
          "min-width": minWidth.value,
          "min-height": minHeight.value,
          w: resize.value.width,
          h: resize.value.height,
          active: true,
          onResizing: onResizeing,
          onDragging
        }, {
          default: withCtx(() => [
            createBaseVNode("canvas", {
              ref_key: "canvasRef",
              ref: canvasRef,
              style: { "position": "absolute", "top": "0", "left": "0" }
            }, null, 512),
            createBaseVNode("div", _hoisted_4, [
              createBaseVNode("div", {
                class: normalizeClass(["magnifier-toolbar-btn flex flex-v flex-ac", {
                  active: !isTheLightOn.value
                }]),
                onClick: _cache[0] || (_cache[0] = ($event) => isTheLightOn.value = !isTheLightOn.value),
                onTouchstart: _cache[1] || (_cache[1] = withModifiers(() => {
                }, ["stop"])),
                onMousedown: _cache[2] || (_cache[2] = withModifiers(() => {
                }, ["stop"]))
              }, [
                createVNode(SvgIcon, {
                  class: "magnifier-toolbar-icon",
                  "icon-class": isTheLightOn.value ? "kaideng" : "guandeng"
                }, null, 8, ["icon-class"]),
                createTextVNode(" " + toDisplayString(isTheLightOn.value ? "关灯" : "开灯"), 1)
              ], 34),
              createBaseVNode("div", {
                class: "magnifier-toolbar-btn flex flex-v flex-ac",
                onTouchstart: _cache[3] || (_cache[3] = withModifiers(() => {
                }, ["stop"])),
                onMousedown: _cache[4] || (_cache[4] = withModifiers(() => {
                }, ["stop"])),
                onClick: onClickClose
              }, [
                createVNode(SvgIcon, {
                  class: "magnifier-toolbar-icon",
                  "icon-class": "delete"
                }),
                _cache[5] || (_cache[5] = createTextVNode(" 关闭 "))
              ], 32)
            ])
          ]),
          _: 1
        }, 8, ["x", "y", "style", "min-width", "min-height", "w", "h"])) : createCommentVNode("", true)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_lang = "";
const index_vue_vue_type_style_index_1_scoped_d03ad5e9_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-d03ad5e9"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
