<!doctype html><html><head><meta charset="UTF-8"/><title>星讲台</title><script src="../../../libs/track/index.js"></script><script>window._track = new WebTrack({
      env: 'dev',
      config: {
        logstore: 'hl-black-board-client-log'
      },
      watchEvents: {
        /** 目前全埋点监控仅限web端，wx小程序暂未支持 */
        clickMount: true,
        // 开启全埋点监控
        page: false // openTelemetry的page有更详细性能数据，关闭原指标
      },
      openTelemetry: {
        // 开启openTelemetry
        fetch: true,
        // 接口trace
        xhr: true,
        // 接口trace
        page: true,
        // 页面性能统计trace
        userInteraction: true // 用户行为trace
      },
      globalData: {
        _project_: 'black-board-client',
        _project_name_: '星讲台'
      },
      terminal: 'windows',
      // 自定义过滤
      filterRule: function(data) {
        try {
          var eventCode = data._event_code_;
          var eventInfo = data._event_info_
          switch (eventCode) {
            case 'CLICK':
              if (!eventInfo) {return true}
              if (typeof eventInfo === 'string') {
                eventInfo = JSON.parse(eventInfo);
              }
              // 点击事件，如果text和module都为空，则不发送埋点数据
              var isOuterText = eventInfo.text;
              var isInnerText;
              var isInnerModule;
              if (eventInfo.dataset) {
                isInnerText = eventInfo.dataset.text;
                isInnerModule = eventInfo.dataset.module;
              }
              return !isOuterText && !isInnerText && !isInnerModule;
          }
          return false;
        } catch {
          return false;
        }
      }
    });</script><script type="module" crossorigin src="../../../js/ResourceDialog.63eb4795.js"></script><link rel="modulepreload" crossorigin href="../../../js/encryptlong.f30353e7.js"><link rel="modulepreload" crossorigin href="../../../js/bootstrap.ab073eb8.js"><link rel="modulepreload" crossorigin href="../../../js/base.649d38c6.js"><link rel="modulepreload" crossorigin href="../../../js/index.8338496f.js"><link rel="modulepreload" crossorigin href="../../../js/rem.5d1b1196.js"><link rel="modulepreload" crossorigin href="../../../js/index.1c1fd1ce.js"><link rel="modulepreload" crossorigin href="../../../js/index.091a2398.js"><link rel="modulepreload" crossorigin href="../../../js/base.676dddc3.js"><link rel="modulepreload" crossorigin href="../../../js/index.4d07c967.js"><link rel="modulepreload" crossorigin href="../../../js/index.4e3d2b08.js"><link rel="modulepreload" crossorigin href="../../../js/typescript.063380fa.js"><link rel="modulepreload" crossorigin href="../../../js/use-form-common-props.6b0d7cd2.js"><link rel="modulepreload" crossorigin href="../../../js/isUndefined.a6a5e481.js"><link rel="modulepreload" crossorigin href="../../../js/hlwhiteboard.b54f17ff.js"><link rel="modulepreload" crossorigin href="../../../js/index.d99eb544.js"><link rel="modulepreload" crossorigin href="../../../js/el-popover.cf188c93.js"><link rel="modulepreload" crossorigin href="../../../js/index.d5831e92.js"><link rel="modulepreload" crossorigin href="../../../js/event.183fce42.js"><link rel="modulepreload" crossorigin href="../../../js/el-button.a9e8e4ae.js"><link rel="modulepreload" crossorigin href="../../../js/IResource.516d6004.js"><link rel="modulepreload" crossorigin href="../../../js/toast.b3d79217.js"><link rel="modulepreload" crossorigin href="../../../js/crypto-js.7319a219.js"><link rel="modulepreload" crossorigin href="../../../js/toastWidget.f897117d.js"><link rel="modulepreload" crossorigin href="../../../js/axios.7d58980a.js"><link rel="modulepreload" crossorigin href="../../../js/file.7001b00a.js"><link rel="modulepreload" crossorigin href="../../../js/ossUtils.5271b247.js"><link rel="modulepreload" crossorigin href="../../../js/position.2e4d825a.js"><link rel="modulepreload" crossorigin href="../../../js/el-image-viewer.10cb6477.js"><link rel="modulepreload" crossorigin href="../../../js/resource.0d7fe94d.js"><link rel="modulepreload" crossorigin href="../../../js/<EMAIL>"><link rel="modulepreload" crossorigin href="../../../js/IComm.3bca3c7b.js"><link rel="stylesheet" href="../../../css/index.046829b5.css"><link rel="stylesheet" href="../../../css/index.cff54096.css"><link rel="stylesheet" href="../../../css/base.36af3b57.css"><link rel="stylesheet" href="../../../css/index.c7438828.css"><link rel="stylesheet" href="../../../css/el-popover.3580f22f.css"><link rel="stylesheet" href="../../../css/index.87cec2d4.css"><link rel="stylesheet" href="../../../css/el-button.1f189f69.css"><link rel="stylesheet" href="../../../css/ossUtils.fd1cea13.css"><link rel="stylesheet" href="../../../css/el-image-viewer.5069c0dd.css"><link rel="stylesheet" href="../../../css/index.b42d7679.css"></head><body><div id="app"></div><script src="../../../libs/iconfont.js?asset"></script></body></html>