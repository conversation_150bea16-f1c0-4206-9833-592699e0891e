<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4853523" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe674;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe674;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">马克笔全</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe672;</span>
                <div class="name">水彩笔全</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe673;</span>
                <div class="name">虚线笔</div>
                <div class="code-name">&amp;#xe673;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dd;</span>
                <div class="name">添加倒计时</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dc;</span>
                <div class="name">布局设置</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6db;</span>
                <div class="name">工作台设置未选中</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6da;</span>
                <div class="name">工作台设置</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">水彩笔</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">马克笔</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">虚线</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d9;</span>
                <div class="name">黑板上传视频</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">baocun</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">缩略</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d8;</span>
                <div class="name">手机上传图片</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d7;</span>
                <div class="name">退出</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">虚线颜色</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">选择</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">画笔-马克笔</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">画笔-虚线</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">画笔-初始</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">清除</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">橡皮</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">马克笔尖</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">画笔-水彩笔</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">水彩笔尖</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">删除无底</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">放大镜</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">随机点名</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">计时器</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">工具箱选中</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">资源库选中</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">资源库</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">工具箱</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">旋转</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">叉</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">直角</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">开灯</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">关灯</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">圆形</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">虚线</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">椭圆</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">直线</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">正方形</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">长方形</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">三角形</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">图形</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">选择</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">橡皮</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">画笔</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">清空</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">撤销</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">收起</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">关机</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">感叹号</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">最小化2</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">叉</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">教师</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">左</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">右</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">课件</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">素材</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">勾</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">切换</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">箭头未展开</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">箭头展开</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">下划线</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">锁</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">飞书</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#x64;</span>
                <div class="name">钉钉</div>
                <div class="code-name">&amp;#x64;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">微信</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">最小化</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">更多应用</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">无网络</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">网络</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">关机</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1748506543691') format('woff2'),
       url('iconfont.woff?t=1748506543691') format('woff'),
       url('iconfont.ttf?t=1748506543691') format('truetype'),
       url('iconfont.svg?t=1748506543691#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-jiantou"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.icon-jiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-makebiquan"></span>
            <div class="name">
              马克笔全
            </div>
            <div class="code-name">.icon-makebiquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuicaibiquan"></span>
            <div class="name">
              水彩笔全
            </div>
            <div class="code-name">.icon-shuicaibiquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuxianbi"></span>
            <div class="name">
              虚线笔
            </div>
            <div class="code-name">.icon-xuxianbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianjiadaojishi"></span>
            <div class="name">
              添加倒计时
            </div>
            <div class="code-name">.icon-tianjiadaojishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bujushezhi"></span>
            <div class="name">
              布局设置
            </div>
            <div class="code-name">.icon-bujushezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuotaishezhiweixuanzhong"></span>
            <div class="name">
              工作台设置未选中
            </div>
            <div class="code-name">.icon-gongzuotaishezhiweixuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuotaishezhi"></span>
            <div class="name">
              工作台设置
            </div>
            <div class="code-name">.icon-gongzuotaishezhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuicaibi"></span>
            <div class="name">
              水彩笔
            </div>
            <div class="code-name">.icon-shuicaibi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-makebi"></span>
            <div class="name">
              马克笔
            </div>
            <div class="code-name">.icon-makebi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuxian"></span>
            <div class="name">
              虚线
            </div>
            <div class="code-name">.icon-xuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-heibanshangchuanshipin"></span>
            <div class="name">
              黑板上传视频
            </div>
            <div class="code-name">.icon-heibanshangchuanshipin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baocun"></span>
            <div class="name">
              baocun
            </div>
            <div class="code-name">.icon-baocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suolve"></span>
            <div class="name">
              缩略
            </div>
            <div class="code-name">.icon-suolve
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoujishangchuantupian"></span>
            <div class="name">
              手机上传图片
            </div>
            <div class="code-name">.icon-shoujishangchuantupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuichu"></span>
            <div class="name">
              退出
            </div>
            <div class="code-name">.icon-tuichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuxianyanse"></span>
            <div class="name">
              虚线颜色
            </div>
            <div class="code-name">.icon-xuxianyanse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanze1"></span>
            <div class="name">
              选择
            </div>
            <div class="code-name">.icon-xuanze1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huabi-makebi"></span>
            <div class="name">
              画笔-马克笔
            </div>
            <div class="code-name">.icon-huabi-makebi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huabi-xuxian"></span>
            <div class="name">
              画笔-虚线
            </div>
            <div class="code-name">.icon-huabi-xuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huabi-chushi"></span>
            <div class="name">
              画笔-初始
            </div>
            <div class="code-name">.icon-huabi-chushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingchu"></span>
            <div class="name">
              清除
            </div>
            <div class="code-name">.icon-qingchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangpi"></span>
            <div class="name">
              橡皮
            </div>
            <div class="code-name">.icon-xiangpi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-makebijian"></span>
            <div class="name">
              马克笔尖
            </div>
            <div class="code-name">.icon-makebijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huabi-shuicaibi"></span>
            <div class="name">
              画笔-水彩笔
            </div>
            <div class="code-name">.icon-huabi-shuicaibi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuicaibijian"></span>
            <div class="name">
              水彩笔尖
            </div>
            <div class="code-name">.icon-shuicaibijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete-plain"></span>
            <div class="name">
              删除无底
            </div>
            <div class="code-name">.icon-delete-plain
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dialog-delete"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-dialog-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-magnifier"></span>
            <div class="name">
              放大镜
            </div>
            <div class="code-name">.icon-magnifier
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-roll-name"></span>
            <div class="name">
              随机点名
            </div>
            <div class="code-name">.icon-roll-name
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-timer"></span>
            <div class="name">
              计时器
            </div>
            <div class="code-name">.icon-timer
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongjuxiang-select"></span>
            <div class="name">
              工具箱选中
            </div>
            <div class="code-name">.icon-gongjuxiang-select
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziyuanku-select"></span>
            <div class="name">
              资源库选中
            </div>
            <div class="code-name">.icon-ziyuanku-select
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziyuanku"></span>
            <div class="name">
              资源库
            </div>
            <div class="code-name">.icon-ziyuanku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongjuxiang"></span>
            <div class="name">
              工具箱
            </div>
            <div class="code-name">.icon-gongjuxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanzhuan"></span>
            <div class="name">
              旋转
            </div>
            <div class="code-name">.icon-xuanzhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              叉
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-plus"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.icon-plus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-minus"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.icon-minus
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-right-triangle"></span>
            <div class="name">
              直角
            </div>
            <div class="code-name">.icon-right-triangle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fullscreen"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-fullscreen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kaideng"></span>
            <div class="name">
              开灯
            </div>
            <div class="code-name">.icon-kaideng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guandeng"></span>
            <div class="name">
              关灯
            </div>
            <div class="code-name">.icon-guandeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-circle"></span>
            <div class="name">
              圆形
            </div>
            <div class="code-name">.icon-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dashed"></span>
            <div class="name">
              虚线
            </div>
            <div class="code-name">.icon-dashed
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ellipse"></span>
            <div class="name">
              椭圆
            </div>
            <div class="code-name">.icon-ellipse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-line"></span>
            <div class="name">
              直线
            </div>
            <div class="code-name">.icon-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.icon-arrow
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-square"></span>
            <div class="name">
              正方形
            </div>
            <div class="code-name">.icon-square
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rect"></span>
            <div class="name">
              长方形
            </div>
            <div class="code-name">.icon-rect
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-triangle"></span>
            <div class="name">
              三角形
            </div>
            <div class="code-name">.icon-triangle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-graphic"></span>
            <div class="name">
              图形
            </div>
            <div class="code-name">.icon-graphic
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xuanze"></span>
            <div class="name">
              选择
            </div>
            <div class="code-name">.icon-xuanze
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-eraser"></span>
            <div class="name">
              橡皮
            </div>
            <div class="code-name">.icon-eraser
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pencil"></span>
            <div class="name">
              画笔
            </div>
            <div class="code-name">.icon-pencil
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-clear"></span>
            <div class="name">
              清空
            </div>
            <div class="code-name">.icon-clear
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-revoke"></span>
            <div class="name">
              撤销
            </div>
            <div class="code-name">.icon-revoke
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouqi"></span>
            <div class="name">
              收起
            </div>
            <div class="code-name">.icon-shouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-guanji"></span>
            <div class="name">
              关机
            </div>
            <div class="code-name">.icon-guanji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gantanhao"></span>
            <div class="name">
              感叹号
            </div>
            <div class="code-name">.icon-gantanhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-refresh"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.icon-refresh
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-min-size2"></span>
            <div class="name">
              最小化2
            </div>
            <div class="code-name">.icon-min-size2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-save"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.icon-save
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jia"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.icon-jia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-close"></span>
            <div class="name">
              叉
            </div>
            <div class="code-name">.icon-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-teacher"></span>
            <div class="name">
              教师
            </div>
            <div class="code-name">.icon-teacher
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-left"></span>
            <div class="name">
              左
            </div>
            <div class="code-name">.icon-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-right"></span>
            <div class="name">
              右
            </div>
            <div class="code-name">.icon-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kejian"></span>
            <div class="name">
              课件
            </div>
            <div class="code-name">.icon-kejian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sucai"></span>
            <div class="name">
              素材
            </div>
            <div class="code-name">.icon-sucai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-select"></span>
            <div class="name">
              勾
            </div>
            <div class="code-name">.icon-select
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fish"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-fish
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-switch"></span>
            <div class="name">
              切换
            </div>
            <div class="code-name">.icon-switch
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-right"></span>
            <div class="name">
              箭头未展开
            </div>
            <div class="code-name">.icon-arrow-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-arrow-down"></span>
            <div class="name">
              箭头展开
            </div>
            <div class="code-name">.icon-arrow-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-underscore"></span>
            <div class="name">
              下划线
            </div>
            <div class="code-name">.icon-underscore
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-phone"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.icon-phone
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lock"></span>
            <div class="name">
              锁
            </div>
            <div class="code-name">.icon-lock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lark"></span>
            <div class="name">
              飞书
            </div>
            <div class="code-name">.icon-lark
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingtalk"></span>
            <div class="name">
              钉钉
            </div>
            <div class="code-name">.icon-dingtalk
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wechat"></span>
            <div class="name">
              微信
            </div>
            <div class="code-name">.icon-wechat
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-min-size"></span>
            <div class="name">
              最小化
            </div>
            <div class="code-name">.icon-min-size
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-more-app"></span>
            <div class="name">
              更多应用
            </div>
            <div class="code-name">.icon-more-app
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-no-net"></span>
            <div class="name">
              无网络
            </div>
            <div class="code-name">.icon-no-net
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-net"></span>
            <div class="name">
              网络
            </div>
            <div class="code-name">.icon-net
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shutdown"></span>
            <div class="name">
              关机
            </div>
            <div class="code-name">.icon-shutdown
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-setting"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.icon-setting
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiantou"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#icon-jiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-makebiquan"></use>
                </svg>
                <div class="name">马克笔全</div>
                <div class="code-name">#icon-makebiquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuicaibiquan"></use>
                </svg>
                <div class="name">水彩笔全</div>
                <div class="code-name">#icon-shuicaibiquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuxianbi"></use>
                </svg>
                <div class="name">虚线笔</div>
                <div class="code-name">#icon-xuxianbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianjiadaojishi"></use>
                </svg>
                <div class="name">添加倒计时</div>
                <div class="code-name">#icon-tianjiadaojishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bujushezhi"></use>
                </svg>
                <div class="name">布局设置</div>
                <div class="code-name">#icon-bujushezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotaishezhiweixuanzhong"></use>
                </svg>
                <div class="name">工作台设置未选中</div>
                <div class="code-name">#icon-gongzuotaishezhiweixuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotaishezhi"></use>
                </svg>
                <div class="name">工作台设置</div>
                <div class="code-name">#icon-gongzuotaishezhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuicaibi"></use>
                </svg>
                <div class="name">水彩笔</div>
                <div class="code-name">#icon-shuicaibi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-makebi"></use>
                </svg>
                <div class="name">马克笔</div>
                <div class="code-name">#icon-makebi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuxian"></use>
                </svg>
                <div class="name">虚线</div>
                <div class="code-name">#icon-xuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-heibanshangchuanshipin"></use>
                </svg>
                <div class="name">黑板上传视频</div>
                <div class="code-name">#icon-heibanshangchuanshipin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baocun"></use>
                </svg>
                <div class="name">baocun</div>
                <div class="code-name">#icon-baocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suolve"></use>
                </svg>
                <div class="name">缩略</div>
                <div class="code-name">#icon-suolve</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoujishangchuantupian"></use>
                </svg>
                <div class="name">手机上传图片</div>
                <div class="code-name">#icon-shoujishangchuantupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuichu"></use>
                </svg>
                <div class="name">退出</div>
                <div class="code-name">#icon-tuichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuxianyanse"></use>
                </svg>
                <div class="name">虚线颜色</div>
                <div class="code-name">#icon-xuxianyanse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanze1"></use>
                </svg>
                <div class="name">选择</div>
                <div class="code-name">#icon-xuanze1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huabi-makebi"></use>
                </svg>
                <div class="name">画笔-马克笔</div>
                <div class="code-name">#icon-huabi-makebi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huabi-xuxian"></use>
                </svg>
                <div class="name">画笔-虚线</div>
                <div class="code-name">#icon-huabi-xuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huabi-chushi"></use>
                </svg>
                <div class="name">画笔-初始</div>
                <div class="code-name">#icon-huabi-chushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingchu"></use>
                </svg>
                <div class="name">清除</div>
                <div class="code-name">#icon-qingchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangpi"></use>
                </svg>
                <div class="name">橡皮</div>
                <div class="code-name">#icon-xiangpi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-makebijian"></use>
                </svg>
                <div class="name">马克笔尖</div>
                <div class="code-name">#icon-makebijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huabi-shuicaibi"></use>
                </svg>
                <div class="name">画笔-水彩笔</div>
                <div class="code-name">#icon-huabi-shuicaibi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuicaibijian"></use>
                </svg>
                <div class="name">水彩笔尖</div>
                <div class="code-name">#icon-shuicaibijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete-plain"></use>
                </svg>
                <div class="name">删除无底</div>
                <div class="code-name">#icon-delete-plain</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dialog-delete"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-dialog-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-magnifier"></use>
                </svg>
                <div class="name">放大镜</div>
                <div class="code-name">#icon-magnifier</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-roll-name"></use>
                </svg>
                <div class="name">随机点名</div>
                <div class="code-name">#icon-roll-name</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-timer"></use>
                </svg>
                <div class="name">计时器</div>
                <div class="code-name">#icon-timer</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongjuxiang-select"></use>
                </svg>
                <div class="name">工具箱选中</div>
                <div class="code-name">#icon-gongjuxiang-select</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuanku-select"></use>
                </svg>
                <div class="name">资源库选中</div>
                <div class="code-name">#icon-ziyuanku-select</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziyuanku"></use>
                </svg>
                <div class="name">资源库</div>
                <div class="code-name">#icon-ziyuanku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongjuxiang"></use>
                </svg>
                <div class="name">工具箱</div>
                <div class="code-name">#icon-gongjuxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanzhuan"></use>
                </svg>
                <div class="name">旋转</div>
                <div class="code-name">#icon-xuanzhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">叉</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-plus"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#icon-plus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-minus"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#icon-minus</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-right-triangle"></use>
                </svg>
                <div class="name">直角</div>
                <div class="code-name">#icon-right-triangle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fullscreen"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-fullscreen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kaideng"></use>
                </svg>
                <div class="name">开灯</div>
                <div class="code-name">#icon-kaideng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guandeng"></use>
                </svg>
                <div class="name">关灯</div>
                <div class="code-name">#icon-guandeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-circle"></use>
                </svg>
                <div class="name">圆形</div>
                <div class="code-name">#icon-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dashed"></use>
                </svg>
                <div class="name">虚线</div>
                <div class="code-name">#icon-dashed</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ellipse"></use>
                </svg>
                <div class="name">椭圆</div>
                <div class="code-name">#icon-ellipse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-line"></use>
                </svg>
                <div class="name">直线</div>
                <div class="code-name">#icon-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#icon-arrow</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-square"></use>
                </svg>
                <div class="name">正方形</div>
                <div class="code-name">#icon-square</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rect"></use>
                </svg>
                <div class="name">长方形</div>
                <div class="code-name">#icon-rect</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-triangle"></use>
                </svg>
                <div class="name">三角形</div>
                <div class="code-name">#icon-triangle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-graphic"></use>
                </svg>
                <div class="name">图形</div>
                <div class="code-name">#icon-graphic</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xuanze"></use>
                </svg>
                <div class="name">选择</div>
                <div class="code-name">#icon-xuanze</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-eraser"></use>
                </svg>
                <div class="name">橡皮</div>
                <div class="code-name">#icon-eraser</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pencil"></use>
                </svg>
                <div class="name">画笔</div>
                <div class="code-name">#icon-pencil</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-clear"></use>
                </svg>
                <div class="name">清空</div>
                <div class="code-name">#icon-clear</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-revoke"></use>
                </svg>
                <div class="name">撤销</div>
                <div class="code-name">#icon-revoke</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouqi"></use>
                </svg>
                <div class="name">收起</div>
                <div class="code-name">#icon-shouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-guanji"></use>
                </svg>
                <div class="name">关机</div>
                <div class="code-name">#icon-guanji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gantanhao"></use>
                </svg>
                <div class="name">感叹号</div>
                <div class="code-name">#icon-gantanhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-refresh"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#icon-refresh</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-min-size2"></use>
                </svg>
                <div class="name">最小化2</div>
                <div class="code-name">#icon-min-size2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-save"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#icon-save</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jia"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#icon-jia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-close"></use>
                </svg>
                <div class="name">叉</div>
                <div class="code-name">#icon-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-teacher"></use>
                </svg>
                <div class="name">教师</div>
                <div class="code-name">#icon-teacher</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-left"></use>
                </svg>
                <div class="name">左</div>
                <div class="code-name">#icon-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-right"></use>
                </svg>
                <div class="name">右</div>
                <div class="code-name">#icon-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kejian"></use>
                </svg>
                <div class="name">课件</div>
                <div class="code-name">#icon-kejian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sucai"></use>
                </svg>
                <div class="name">素材</div>
                <div class="code-name">#icon-sucai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-select"></use>
                </svg>
                <div class="name">勾</div>
                <div class="code-name">#icon-select</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fish"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-fish</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-switch"></use>
                </svg>
                <div class="name">切换</div>
                <div class="code-name">#icon-switch</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-right"></use>
                </svg>
                <div class="name">箭头未展开</div>
                <div class="code-name">#icon-arrow-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow-down"></use>
                </svg>
                <div class="name">箭头展开</div>
                <div class="code-name">#icon-arrow-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-underscore"></use>
                </svg>
                <div class="name">下划线</div>
                <div class="code-name">#icon-underscore</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-phone"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#icon-phone</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lock"></use>
                </svg>
                <div class="name">锁</div>
                <div class="code-name">#icon-lock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lark"></use>
                </svg>
                <div class="name">飞书</div>
                <div class="code-name">#icon-lark</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingtalk"></use>
                </svg>
                <div class="name">钉钉</div>
                <div class="code-name">#icon-dingtalk</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wechat"></use>
                </svg>
                <div class="name">微信</div>
                <div class="code-name">#icon-wechat</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-min-size"></use>
                </svg>
                <div class="name">最小化</div>
                <div class="code-name">#icon-min-size</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-more-app"></use>
                </svg>
                <div class="name">更多应用</div>
                <div class="code-name">#icon-more-app</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-no-net"></use>
                </svg>
                <div class="name">无网络</div>
                <div class="code-name">#icon-no-net</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-net"></use>
                </svg>
                <div class="name">网络</div>
                <div class="code-name">#icon-net</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shutdown"></use>
                </svg>
                <div class="name">关机</div>
                <div class="code-name">#icon-shutdown</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-setting"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#icon-setting</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
