<?xml version="1.0" encoding="UTF-8"?>
<svg width="148px" height="220px" viewBox="0 0 148 220" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 15备份</title>
    <defs>
        <path d="M76.6639088,8.05558668 C88.7835375,-3.24181133 107.766788,-2.5752617 119.064186,9.54436694 C130.361584,21.6639956 129.695035,40.6472464 117.575406,51.9446445 C104.256196,64.360241 93.054984,79.4247979 83.9413913,97.2958199 C69.0969075,126.404658 61.1968783,154.241802 59.9689128,180.943106 C59.2077471,197.494155 45.1734241,210.294385 28.6223749,209.533219 C12.0713258,208.772054 -0.72890381,194.737731 0.0322618869,178.186681 C1.67073499,142.559158 11.9142791,106.464227 30.4905031,70.0377467 C42.7231286,46.0505523 58.1243903,25.3373307 76.6639088,8.05558668 Z" id="path-1"></path>
        <filter x="-2.4%" y="-1.4%" width="104.7%" height="102.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="2.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.37254902   0 0 0 0 0.321568627   0 0 0 0 0.890196078  0 0 0 0.592848558 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="V1.2.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="白板备份" transform="translate(-1458.8065, -1045.24)">
            <g id="编组-46" transform="translate(1458.8065, 1045.24)">
                <g id="编组-15备份" transform="translate(74.1645, 110.8525) rotate(6) translate(-74.1645, -110.8525)translate(10.6046, 6.0698)">
                    <g id="路径-2" opacity="0.25" fill-rule="nonzero">
                        <use fill="#FFFFFF" xlink:href="#path-1"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    </g>
                    <path d="M75.3738499,15.9725149 C87.9708473,3.65960665 103.958802,3.65960665 113.421759,13.713908 C122.884715,23.7682094 122.884715,38.6398542 109.642248,52.382361 C106.576655,55.5637117 99.8134226,63.427437 97.8617919,65.7983452 C89.1206017,76.4174444 75.3738499,85.2643447 60.9708473,75.8754664 C46.5678448,66.4865881 48.3810387,50.2326267 55.8806489,38.6398542 C58.7297433,34.2357715 61.8499231,30.1109709 65.2399752,26.2759588 C67.8465113,23.3273027 71.3694475,19.8866095 75.3738499,15.9725149 Z" id="路径-2备份-2" fill="#5F52E3" fill-rule="nonzero"></path>
                    <circle id="椭圆形" fill="#FFFFFF" cx="32.9708473" cy="158.516945" r="10"></circle>
                    <circle id="椭圆形备份-7" fill="#FFFFFF" cx="49.9708473" cy="97.5169447" r="7"></circle>
                    <path d="M83.9708473,48.6848452 C86.1799863,48.6848452 87.9708473,46.8939842 87.9708473,44.6848452 C87.9708473,42.4757062 86.1799863,40.6848452 83.9708473,40.6848452 C81.7617083,40.6848452 79.9708473,42.4757062 79.9708473,44.6848452 C79.9708473,46.8939842 81.7617083,48.6848452 83.9708473,48.6848452 Z" id="椭圆形备份-8" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </g>
</svg>