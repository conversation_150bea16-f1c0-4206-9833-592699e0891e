#ifndef WHITEBOARDVIEW_H
#define WHITEBOARDVIEW_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QScrollArea>
#include <QSharedPointer>

#include <QLabel>
#include <QMap>
#include <QPushButton>
#include <QJsonObject>
#include <QJsonArray>
#include <QImageWriter>
#include <QApplication>
#include <QScreen>
#include <QWindow>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QAction>
#include <src/components/sidebar/magnifier/MagnifierWidget.h>
#include <src/components/sidebar/magnifier/MagnifierFullWidget.h>
#include <src/components/octupos/OctopusDiscWidget.h>
#include "../../whiteboard/core/WhiteBoardTypes.h"
#include "../../whiteboard/core/WhiteBoard.h"
#include "../sidebar/SideBarConstants.h"
#include "src/models/classroommodel.h"
#include "../../utils/ScreenUtils.h"

// 前向声明
class FloatMenuWidget;
class WindowManager;
class QCefView;
class CefViewWidget;
class ZIndexManager;
class SideBarWidget;
class ToolboxPopup;
class BottomBarWidget;
class WhiteBoard;

/**
 * @brief 白板视图组件
 * 
 * 整合白板画布和浮动菜单功能的复合组件，提供完整的白板交互体验。
 * 该组件负责：
 * 1. 管理画布视图(CanvasView)的显示和交互
 * 2. 集成浮动菜单(FloatMenuWidget)的功能
 * 3. 协调两个组件之间的数据传递和状态同步
 * 4. 提供统一的工具管理和性能监控接口
 */
class WhiteboardView : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit WhiteboardView(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~WhiteboardView();

    /**
     * @brief 获取浮动菜单组件
     * @return FloatMenuWidget指针
     */
    FloatMenuWidget* floatMenuWidget() const;

    /**
     * @brief 显示浮动菜单
     */
    void showFloatMenu();

    /**
     * @brief 隐藏浮动菜单
     */
    void hideFloatMenu();

    /**
     * @brief 切换浮动菜单显示状态
     */
    void toggleFloatMenu();

    /**
     * @brief 获取浮动菜单是否可见
     * @return 是否可见
     */
    bool isFloatMenuVisible() const;

    /**
     * @brief 设置当前工具类型
     * @param toolType 工具类型
     */
    void setCurrentTool(ToolType toolType);

    /**
     * @brief 获取当前工具类型
     * @return 当前工具类型
     */
    ToolType currentToolType() const;

    /**
     * @brief 设置工具颜色
     * @param color 颜色
     */
    void setToolColor(const QColor& color);

    /**
     * @brief 设置工具线宽
     * @param width 线宽
     */
    void setToolStrokeWidth(qreal width);

    /**
     * @brief 设置画笔类型
     * @param type 画笔类型 (0=虚线, 1=马克笔, 2=实线笔)
     */
    void setPenType(int type);

    /**
     * @brief 清除画布
     */
    void clearCanvas();

    /**
     * @brief 保存画布
     */
    void saveBoard(QPoint pos);

    /**
     * @brief 撤销操作
     */
    void undo();

    /**
     * @brief 恢复操作
     */
    void redo();

    /**
     * @brief 获取撤销是否可用
     * @return 是否可撤销
     */
    bool canUndo() const;

    /**
     * @brief 获取恢复是否可用
     * @return 是否可恢复
     */
    bool canRedo() const;



    /**
     * @brief 获取Z-Index管理器
     * @return ZIndexManager指针
     */
    ZIndexManager* zIndexManager() const;

    /**
     * @brief 临时置顶组件
     * @param widget 要置顶的组件
     */
    void bringComponentToTop(QWidget* widget);

    /**
     * @brief 恢复组件到原始层级
     * @param widget 组件
     */
    void restoreComponentLevel(QWidget* widget);

    /**
     * @brief 打印当前层级状态(调试用)
     */
    void printZIndexStatus() const;

    /**
     * @brief 安全地更新右侧工具栏和工具箱配置（保证在主线程执行）
     */
    void updateToolsConfig();

    /**
     * @brief 显示底部操作栏组件
     */
    void showBottomBarWidget();
    /**
     * @brief 隐藏底部操作栏组件
     */
    void hideBottomBarWidget();
    /**
     * @brief 显示侧边栏组件
     */
    void showSideBarWidget();
    /**
     * @brief 隐藏侧边栏组件
     */
    void hideSideBarWidget();


    /**
     * 创建并显示小章鱼圆盘
     */
    void createOctopusDisc();
    /**
     * 销毁小章鱼圆盘
     */
    void destroyOctopusDisc();
public slots:
    /**
     * @brief 响应窗口大小变化
     */
    void onWindowSizeChanged();

    /**
     * @brief 强制画布保持在底层
     */
    void forceCanvasToBottom();



    /**
     * @brief 切换资源跟踪状态
     * @param resourceKey 资源键值
     */
    void toggleResourceTrace(const QString& resourceKey);

    /**
     * @brief 通过资源id查询相关轨迹
     */
    QStringList getTraceResourceKeys(const QString& resourceId);

    /**
     * 保存资源图片
     * @param saveRequests 保存请求数组，每个请求包含index和imagePath
     * @return 保存结果数组，每个结果包含index、imagePath和isSuccess
     */
    QJsonArray saveResourceImages(const QJsonArray& saveRequests);

signals:
    /**
     * @brief 当前工具改变信号
     * @param toolType 新的工具类型
     */
    void currentToolChanged(ToolType toolType);

    /**
     * @brief 工具颜色改变信号
     * @param color 新颜色
     */
    void toolColorChanged(const QColor& color);

    /**
     * @brief 工具线宽改变信号
     * @param width 新线宽
     */
    void toolStrokeWidthChanged(qreal width);

    /**
     * @brief 画笔类型改变信号
     * @param type 画笔类型 (0=虚线, 1=马克笔, 2=实线笔)
     */
    void penTypeChanged(int type);

    /**
     * @brief 画布清除信号
     */
    void canvasCleared();

    /**
     * @brief 撤销/恢复状态改变信号
     * @param canUndo 是否可撤销
     * @param canRedo 是否可恢复
     */
    void undoRedoStateChanged(bool canUndo, bool canRedo);

    /**
     * @brief 浮动菜单状态改变信号
     * @param visible 是否可见
     */
    void floatMenuVisibilityChanged(bool visible);



protected:
    /**
     * @brief 重写移动事件
     * @param event 移动事件
     */
    void moveEvent(QMoveEvent* event) override;

    /**
     * @brief 重写大小调整事件
     * @param event 大小调整事件
     */
    void resizeEvent(QResizeEvent* event) override;

    /**
     * @brief 重写显示事件
     * @param event 显示事件
     */
    void showEvent(QShowEvent* event) override;

    /**
     * @brief 重写事件过滤器（仅用于特定组件）
     * @param obj 事件对象
     * @param event 事件
     * @return 是否处理事件
     */
    bool eventFilter(QObject* obj, QEvent* event) override;

private slots:
    /**
     * @brief 处理工具管理器工具改变
     * @param toolType 工具类型
     */
    void onToolManagerToolChanged(ToolType toolType);

    /**
     * @brief 处理浮动菜单工具选择
     * @param toolName 工具名称
     */
    void onFloatMenuToolSelected(const QString& toolName);

    /**
     * @brief 处理浮动菜单颜色改变
     * @param color 新颜色
     */
    void onFloatMenuColorChanged(const QColor& color);

    /**
     * @brief 处理浮动菜单线宽改变
     * @param width 新线宽
     */
    void onFloatMenuStrokeWidthChanged(qreal width);

    /**
     * @brief 处理命令系统状态改变
     */
    void onCommandSystemStateChanged();

private:
    /**
     * @brief 初始化挂在前端静态资源
     */
    void initializeWebview();

    /**
     * @brief 初始化新白板系统
     */
    void initializeWhiteBoard();

    /**
     * @brief 初始化浮动菜单
     */
    void initializeFloatMenu();



    /**
     * @brief 初始化侧边栏
     */
    void initializeSideBar();

    /**
     * @brief 初始化底部操作栏
     */
    void initializeBottomBar();

    /**
     * @brief 连接信号和槽
     */
    void connectSignalsAndSlots();



    /**
     * @brief 初始化UI层级管理
     */
    void initializeUILayerManagement();

    /**
     * @brief 初始化ZMQ
     */
    void initializeZmq();

    /**
     * @brief 初始化桥接
     */
    void initializeBridge();

    /**
     * @brief 确保UI组件始终在顶层
     */
    void ensureUIComponentsOnTop();

    /**
     * @brief 设置组件Z-Order层级
     */
    void setupComponentZOrder();

    /**
     * @brief 强制重新应用组件Z-Order层级（用于响应窗口激活事件）
     */
    Q_INVOKABLE void forceReapplyComponentZOrder();

    /**
     * @brief 注册所有组件到Z-Index管理器
     */
    void registerAllComponentsToZIndexManager();

    /**
     * @brief 更新画布大小
     */
    void updateCanvasSize();


    /**
     * @brief 同步浮动菜单状态到工具管理器
     */
    void syncFloatMenuStateToToolManager();

    /**
     * @brief 从浮动菜单同步橡皮擦大小到橡皮擦工具
     */
    void syncEraserSizeFromFloatMenu();


    /**
     * @brief 处理图形工具选择
     * @param toolType 图形工具类型（-1表示取消选择）
     */
    void handleGraphicToolSelection(int toolType);

    /**
     * @brief 设置窗口透明度和全屏属性
     */
    void setupWindowTransparency();

    /**
     * @brief 启用鼠标穿透功能（简化版）
     */
    void enableMousePassThrough();

    /**
     * @brief 禁用鼠标穿透功能
     */
    void disableMousePassThrough();

    /**
     * @brief 创建工具信息对象
     * @param config 工具配置信息（包含工具名称、图标、视图类型、点击类型等）
     * @return 工具信息对象
     */
    static SideBarConstants::ToolInfo createToolInfo(const json &config);

    /**
     * @brief 退出教室
     */
    void exitClassroom();
    /**
     * @brief 插入图片白板
     * @param data 图片数据
     */
    void insertImageWhiteboard(const nlohmann::json &data);

    /**
     * @brief 隐藏Qt应用窗口
     */
    void hideQtApplication();

    /**
     * @brief 切换到课件选择模式
     */
    void switchToCoursewareSelectionMode();

    /**
     * @brief 切换到绘制选择模式
     */
    void switchToDrawSelectionMode();

    /**
     * 处理缩略图隐藏逻辑
     */
    QVariant  handleThumbnailHide(QObject* obj, QEvent* event);

    /**
     * @brief 显示AI小结章鱼
     */
    void showAISummaryOctopus();

    
    /**
     * @brief 关闭AI总结章鱼
     */
    void closeAISummaryOctopus();

    /**
     * @brief 打开课件总结弹窗
     */
    void openClassSummaryDialog();

    /**
     * @brief 打开设置弹窗
     */
    void openSettingDialog();

    /**
     * @brief 打开未开启章鱼弹窗
     */
    void openNotOpenOctopusDialog();

    /**
     * @brief 开始旋转AI小结章鱼
     */
    void startRotateAISummaryOctopus();

    /**
     * @brief 停止旋转AI小结章鱼
     */
    void stopRotateAISummaryOctopus();

    /**
     * @brief 显示AI总结消息
     * @param message 消息内容
     * @param duration 消息显示时长（毫秒）
     */
    void showAISummaryMsg(const QString &message, int duration);

public:
    /**
     * @brief 获取UI控件区域信息（简化版，所有UI都是子控件）
     * @return UI控件区域列表
     */
    QVector<QRect> getUIRegions() const;

private slots:
    /**
     * @brief 处理侧边栏按钮点击
     * @param toolInfo 工具信息（包含按钮类型、工具名称、配置信息等）
     */
    void onSideBarButtonClicked(const SideBarConstants::ToolInfo& toolInfo);

    /**
     * @brief 使用工具配置信息处理工具点击
     * @param toolInfo 工具信息（包含URL、位置、大小等配置）
     */
    void handleToolWithConfig(const SideBarConstants::ToolInfo& toolInfo);

    /**
     * @brief 通用的CefViewWidget处理方法
     * @param toolInfo 工具信息
     */
    void handleCefViewWithConfig(const SideBarConstants::ToolInfo& toolInfo);

    /**
     * @brief 获取指定工具的CefViewWidget
     * @param toolName 工具名称
     * @return CefViewWidget指针，如果不存在返回nullptr
     */
    CefViewWidget* getCefView(const QString& toolName) const;

    /**
     * @brief 显示工具箱弹窗
     * @param position 显示位置（全局坐标）
     */
    void showToolboxPopup(const QPoint& position);

    /**
     * @brief 隐藏工具箱弹窗
     */
    void hideToolboxPopup();

    /**
     * @brief 处理工具箱弹窗中的工具点击
     * @param toolInfo 工具信息
     */
    void onToolboxPopupToolClicked(const SideBarConstants::ToolInfo& toolInfo);

    /**
     * @brief 创建侧边栏工具配置
     * @return 工具配置列表
     */
    QList<SideBarConstants::ToolInfo> createSideBarToolsConfig();

    /**
     * @brief 处理Electron功能
     * @param toolInfo 工具信息
     */
    void handleElectronFunction(const SideBarConstants::ToolInfo& toolInfo);

    /**
     * @brief 获取工具箱工具配置（根据showToolBox字段过滤）
     * @return 需要在工具箱中显示的工具配置列表
     */
    QList<SideBarConstants::ToolInfo> getToolboxToolsConfig();

    /**
     * @brief 处理Qt工具
     */
    void handleQtTool(const SideBarConstants::ToolInfo &toolInfo);

    /**
     * @brief 安全打开工具窗口（主线程执行，避免重复打开）
     * @param toolName 工具名称
     */
    void safeOpenToolWidget(const QString& toolName);


private:
    // UI组件
    WhiteBoard* m_whiteBoard;               ///< 新白板系统核心组件
    FloatMenuWidget* m_floatMenuWidget;     ///< 浮动菜单组件
    // 状态管理
    bool m_isInitialized;                   ///< 是否已初始化
    QSize m_lastCanvasSize;                 ///< 上次画布大小

    // 窗口管理
    ZIndexManager* m_zIndexManager;         ///< Z-Index层级管理器

    // CefViewWidget统一管理
    QMap<QString, CefViewWidget*> m_cefViews;  ///< CefViewWidget容器，key为工具名称
   
    // 鼠标穿透状态
    bool m_mousePassThroughEnabled;         ///< 鼠标穿透是否启用

    // 侧边栏组件
    SideBarWidget* m_sideBarWidget;         ///< 侧边栏组件
    ToolboxPopup* m_toolboxPopup;           ///< 工具箱弹窗组件

    // 底部操作栏组件
    BottomBarWidget* m_bottomBarWidget;     ///< 底部操作栏组件

    // 小章鱼圆盘
    QPointer<OctopusDiscWidget> m_octopusDiscWidget; ///< 小章鱼圆盘组件

    // 缩略图配置
    SideBarConstants::ToolInfo m_thumbnailToolInfo; ///< 缩略图配置

    // 资源轨迹管理
    QMap<QString, QJsonObject> m_resourceTraceMap;
    
    // 资源轨迹图片管理
    QMap<QString, QImage> m_resourceImageMap;

    QString m_currentResourceKey;            ///< 当前使用的资源key
    QString m_currentResourceId;
    ClassroomModel classroomModel;          ///< 上课状态数据
    QList<SideBarConstants::ToolInfo> m_toolsConfig;   ///< 工具配置

    // 放大镜组件
    QPointer<MagnifierFullWidget> m_magnifierFullWidget = nullptr;
    // 画笔类型
    PenType m_penType;

};

#endif // WHITEBOARDVIEW_H
