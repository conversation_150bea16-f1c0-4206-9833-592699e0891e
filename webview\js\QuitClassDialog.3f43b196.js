import { _ as _export_sfc, a as Bridge, d as initLoggerWidget, p as pinia } from "./index.8338496f.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { d as defineComponent, b as openBlock, m as createElementBlock, j as createBaseVNode, F as Fragment, R as renderList, p as createVNode, f as withCtx, G as createTextVNode, r as ref, e as createBlock, D as createCommentVNode, ad as createApp } from "./bootstrap.ab073eb8.js";
import { D as Dialog } from "./index.091a2398.js";
import "./base.676dddc3.js";
import { E as ElButton } from "./el-button.a9e8e4ae.js";
import { Q as QT_CEF_MESSAGE_TYPE, C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.3bca3c7b.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./index.1c1fd1ce.js";
import "./use-form-common-props.6b0d7cd2.js";
const _hoisted_1 = { class: "quit-class" };
const _hoisted_2 = { class: "quit-class-line" };
const _hoisted_3 = { class: "quit-class-btns" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "index",
  emits: ["close", "confirm"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    function onClose() {
      emits("close");
    }
    function onConfirm() {
      emits("confirm");
    }
    return (_ctx, _cache) => {
      const _component_el_button = ElButton;
      return openBlock(), createElementBlock("div", _hoisted_1, [
        _cache[2] || (_cache[2] = createBaseVNode("div", { class: "quit-class-title" }, "是否需要退出课堂", -1)),
        createBaseVNode("div", _hoisted_2, [
          (openBlock(), createElementBlock(Fragment, null, renderList(27, (item) => {
            return createBaseVNode("div", {
              key: item,
              class: "quit-class-line-item"
            });
          }), 64))
        ]),
        createBaseVNode("div", _hoisted_3, [
          createVNode(_component_el_button, { onClick: onClose }, {
            default: withCtx(() => _cache[0] || (_cache[0] = [
              createTextVNode("取消")
            ])),
            _: 1
          }),
          createVNode(_component_el_button, {
            class: "quit-button",
            onClick: onConfirm
          }, {
            default: withCtx(() => _cache[1] || (_cache[1] = [
              createTextVNode("确认退出")
            ])),
            _: 1
          })
        ])
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_14e3147a_lang = "";
const __unplugin_components_0 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-14e3147a"]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const quitDialogVisible = ref(true);
    function onClose() {
      Bridge.getInstance().callVoid(QT_CEF_MESSAGE_TYPE.CLOSE);
    }
    function onConfirm() {
      Bridge.getInstance().callVoid(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_EXIT);
    }
    return (_ctx, _cache) => {
      const _component_QuitClass = __unplugin_components_0;
      const _component_Dialog = Dialog;
      return quitDialogVisible.value ? (openBlock(), createBlock(_component_Dialog, {
        key: 0,
        class: "quit-class-dialog",
        visible: quitDialogVisible.value,
        "show-close": "",
        onClose
      }, {
        default: withCtx(() => [
          createVNode(_component_QuitClass, {
            onClose,
            onConfirm
          })
        ]),
        _: 1
      }, 8, ["visible"])) : createCommentVNode("", true);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_49b97567_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-49b97567"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
