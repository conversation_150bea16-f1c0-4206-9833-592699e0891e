<!doctype html><html><head><meta charset="UTF-8"/><title>星讲台测试</title><script src="./libs/track/index.js"></script><script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js" onload="window.mathJaxIsReady = true"></script><script>window.MathJax = {
        tex: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)']
          ] // 启用 $...$ 作为行内公式分隔符
        },
        chtml: {
          // 禁用远程字体，使用本地字体
          fontURL: '',
          adaptiveCSS: false,
          matchFontHeight: false,
          scale: 1.0,
          minScale: 0.5,
          // 使用本地字体
          font: 'mathjax',
          // 禁用字体检查
          fontCache: 'none'
        },
        startup: {
          ready: function () {
            console.log('MathJax 加载完成')
            window.mathJaxIsReady = true
            MathJax.startup.defaultReady()
          }
        }
      }</script><script>window._track = new WebTrack({
        env: 'dev',
        config: {
          logstore: 'hl-black-board-client-log'
        },
        watchEvents: {
          /** 目前全埋点监控仅限web端，wx小程序暂未支持 */
          clickMount: true,
          // 开启全埋点监控
          page: false // openTelemetry的page有更详细性能数据，关闭原指标
        },
        openTelemetry: {
          // 开启openTelemetry
          fetch: true,
          // 接口trace
          xhr: true,
          // 接口trace
          page: true,
          // 页面性能统计trace
          userInteraction: true // 用户行为trace
        },
        globalData: {
          _project_: 'black-board-client',
          _project_name_: '星讲台'
        },
        terminal: 'windows',
        // 自定义过滤
        filterRule: function (data) {
          try {
            var eventCode = data._event_code_
            var eventInfo = data._event_info_
            switch (eventCode) {
              case 'CLICK':
                if (!eventInfo) {
                  return true
                }
                if (typeof eventInfo === 'string') {
                  eventInfo = JSON.parse(eventInfo)
                }
                // 点击事件，如果text和module都为空，则不发送埋点数据
                var isOuterText = eventInfo.text
                var isInnerText
                var isInnerModule
                if (eventInfo.dataset) {
                  isInnerText = eventInfo.dataset.text
                  isInnerModule = eventInfo.dataset.module
                }
                return !isOuterText && !isInnerText && !isInnerModule
            }
            return false
          } catch {
            return false
          }
        }
      })</script><script type="module" crossorigin src="./js/main.9a6b8281.js"></script><link rel="modulepreload" crossorigin href="./js/encryptlong.f30353e7.js"><link rel="modulepreload" crossorigin href="./js/bootstrap.ab073eb8.js"><link rel="modulepreload" crossorigin href="./js/base.649d38c6.js"><link rel="modulepreload" crossorigin href="./js/index.8338496f.js"><link rel="modulepreload" crossorigin href="./js/stopDrag.9e5623d9.js"><link rel="modulepreload" crossorigin href="./js/index.1c1fd1ce.js"><link rel="modulepreload" crossorigin href="./js/base.676dddc3.js"><link rel="modulepreload" crossorigin href="./js/index.4d07c967.js"><link rel="modulepreload" crossorigin href="./js/index.4e3d2b08.js"><link rel="modulepreload" crossorigin href="./js/typescript.063380fa.js"><link rel="modulepreload" crossorigin href="./js/use-form-common-props.6b0d7cd2.js"><link rel="modulepreload" crossorigin href="./js/isUndefined.a6a5e481.js"><link rel="modulepreload" crossorigin href="./js/hlwhiteboard.b54f17ff.js"><link rel="modulepreload" crossorigin href="./js/index.d99eb544.js"><link rel="modulepreload" crossorigin href="./js/index.d5831e92.js"><link rel="modulepreload" crossorigin href="./js/toast.b3d79217.js"><link rel="modulepreload" crossorigin href="./js/crypto-js.7319a219.js"><link rel="modulepreload" crossorigin href="./js/toastWidget.f897117d.js"><link rel="modulepreload" crossorigin href="./js/axios.7d58980a.js"><link rel="modulepreload" crossorigin href="./js/index.091a2398.js"><link rel="modulepreload" crossorigin href="./js/school.b2d8c825.js"><link rel="modulepreload" crossorigin href="./js/event.183fce42.js"><link rel="modulepreload" crossorigin href="./js/el-button.a9e8e4ae.js"><link rel="modulepreload" crossorigin href="./js/directive.aac8de61.js"><link rel="modulepreload" crossorigin href="./js/theme.80fd53af.js"><link rel="modulepreload" crossorigin href="./js/classroom.478fd06d.js"><link rel="stylesheet" href="./css/index.046829b5.css"><link rel="stylesheet" href="./css/base.36af3b57.css"><link rel="stylesheet" href="./css/index.c7438828.css"><link rel="stylesheet" href="./css/index.87cec2d4.css"><link rel="stylesheet" href="./css/index.cff54096.css"><link rel="stylesheet" href="./css/el-button.1f189f69.css"><link rel="stylesheet" href="./css/theme.4b7ce67a.css"><link rel="stylesheet" href="./css/index.2cbbb96d.css"><link rel="stylesheet" href="./css/el-loading.4a3a8d0f.css"></head><body><div id="app"></div><script src="./libs/iconfont.js?asset"></script><script src="./libs/pptRenderer_v3.9.12.js"></script></body></html>