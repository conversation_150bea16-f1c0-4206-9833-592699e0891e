#ifndef WHITEBOARD_H
#define WHITEBOARD_H

#include <QObject>
#include <QColor>
#include <QPointF>
#include <QWidget>
#include <QPointer>
#include <QScopedPointer>
#include "WhiteBoardTypes.h"

// 画笔类型枚举
enum class PenType {
    Solid = 0,      // 实线笔
    Dashed = 1,     // 虚线笔
    Highlighter = 2 // 荧光笔
};

class WhiteBoardWidget;
class WhiteBoardScene;
class SceneSerializer;
class ImageExporter;
class CommandManager;

/**
 * @brief 简化的白板核心API类 - 提供纯API接口，不包含UI组件
 *
 * 核心功能：
 * 1. 提供完整的白板功能API
 * 2. 管理WhiteBoardWidget和WhiteBoardScene
 * 3. 暴露工具切换、属性设置等接口给外部UI
 * 4. 支持4K屏幕适配和性能监控
 */
class WhiteBoard : public QObject
{
    Q_OBJECT

public:
    // 使用公共的工具类型枚举
    using ToolType = ::ToolType;

public:
    explicit WhiteBoard(QObject* parent = nullptr);
    ~WhiteBoard();

    // 初始化
    void initialize();

    // 组件访问 - 返回视图组件供外部UI使用
    WhiteBoardWidget* getView() const;
    WhiteBoardScene* getScene() const;

    // 工具管理API
    void setCurrentTool(ToolType toolType);
    ToolType getCurrentTool() const;
    void cancelCurrentOperation();
    void finishCurrentOperation();

    // 绘制属性API
    void setDrawingColor(const QColor& color);
    void setLineWidth(qreal width);
    void setPenStyle(Qt::PenStyle style);
    void setPenType(PenType type);  // 新增：设置画笔类型
    void setOpacity(qreal opacity);
    void setFillColor(const QColor& color);
    void setFilled(bool filled);
    void setEraserSize(const QSizeF& size);


    // 属性获取API
    QColor getDrawingColor() const;
    qreal getLineWidth() const;
    Qt::PenStyle getPenStyle() const;
    PenType getPenType() const;  // 新增：获取画笔类型
    qreal getOpacity() const;
    QColor getFillColor() const;
    bool isFilled() const;

    // 场景管理API
    void clearScene();
    void clearAll();                    // 清空所有内容（图形+命令历史）
    void clearHistory();                // 仅清空命令历史
    void clearWithConfirmation();       // 带确认的清空（返回是否执行）
    void moveAllToHistory();
    int getActiveItemCount() const;
    int getHistoryItemCount() const;

    // 清空状态查询
    bool hasContent() const;            // 是否有任何内容
    bool canClear() const;              // 是否可以清空



    // 4K屏幕适配API
    void updateDPIScale(qreal scale);
    qreal getDPIScale() const;

    // 视图控制API
    void setSceneRect(const QRectF& rect);
    QRectF getSceneRect() const;

    // 命令系统API
    bool canUndo() const;
    bool canRedo() const;
    bool undo();
    bool redo();
    void clearCommandHistory();

    // 数据序列化API
    bool saveToFile(const QString& filePath, const QVariantMap& metadata = QVariantMap());
    bool loadFromFile(const QString& filePath, QVariantMap* metadata = nullptr);
    QJsonObject exportToJson(const QVariantMap& metadata = QVariantMap(), bool includeCommandStack = true);
    bool importFromJson(const QJsonObject& jsonData, QVariantMap* metadata = nullptr, bool mergeMode = true);
    QString exportToJsonString(bool prettyFormat = true);

    // 带操作栈的序列化API
    /**
     * @brief 导出场景和操作栈到JSON
     * @param metadata 元数据
     * @param includeCommandStack 是否包含操作栈
     * @param mergeMode 导入时是否使用合并模式
     * @return 包含场景和操作栈的JSON对象
     */
    QJsonObject exportToJsonWithCommands(const QVariantMap& metadata = QVariantMap(),
                                        bool includeCommandStack = true,
                                        bool mergeMode = true);

    /**
     * @brief 从JSON导入场景和操作栈
     * @param jsonData JSON数据
     * @param metadata 元数据输出
     * @param mergeMode 是否合并操作栈（true=合并，false=替换）
     * @return 是否导入成功
     */
    bool importFromJsonWithCommands(const QJsonObject& jsonData,
                                   QVariantMap* metadata = nullptr,
                                   bool mergeMode = true);

    // 图片导出API
    bool exportToImage(const QString& filePath, const QString& format = "PNG",
                      const QSize& size = QSize(), const QVariantMap& options = QVariantMap());
    QPixmap exportToPixmap(const QSize& size = QSize(), const QVariantMap& options = QVariantMap());
    QImage exportToQImage(const QSize& size = QSize(), const QVariantMap& options = QVariantMap());
    bool exportMultipleFormats(const QString& baseFilePath, const QStringList& formats,
                              const QSize& size = QSize(), const QVariantMap& options = QVariantMap());

    // 获取命令管理器
    class CommandManager* getCommandManager() const;

    // 图片插入API
    bool insertImage(const QString& imagePath, const QPointF& position = QPointF(0, 0),
                    qreal displayWidth = 0, qreal maxHeight = 800);
    bool insertImageAtCenter(const QString& imagePath, qreal displayWidth = 0, qreal maxHeight = 800);

    // 图片工具配置
    void setDefaultImageWidth(qreal width);
    void setDefaultImageMaxHeight(qreal maxHeight);
    qreal getDefaultImageWidth() const;
    qreal getDefaultImageMaxHeight() const;

    // 多指绘制控制API
    void setMultiTouchEnabled(bool enabled);
    bool isMultiTouchEnabled() const;
    int getMaxTouchPoints() const;

signals:
    // 工具状态信号
    void toolChanged(ToolType oldTool, ToolType newTool);
    void drawingStarted(ToolType tool);
    void drawingFinished(ToolType tool);
    void operationCancelled();

    // 属性变化信号
    void colorChanged(const QColor& color);
    void lineWidthChanged(qreal width);
    void opacityChanged(qreal opacity);

    // 场景变化信号
    void sceneChanged();
    void itemCountChanged(int activeCount, int historyCount);



    // 命令系统信号
    void canUndoChanged(bool canUndo);
    void canRedoChanged(bool canRedo);
    void commandExecuted();
    void operationChanged(const QString& operationType, const QString& description);

    // 清空相关信号
    void aboutToClear();                // 即将清空
    void sceneCleared();                // 场景已清空
    void allCleared();                  // 所有内容已清空
    void historyCleared();              // 命令历史已清空
    void contentChanged();              // 内容发生变化


private slots:
    void onToolChanged(ToolType tool);
    void onDrawingStarted(int touchId);
    void onDrawingFinished(int touchId);
    void onSceneChanged();


private:
    // 初始化方法
    void setupConnections();
    void updatePenFromType();  // 根据画笔类型更新画笔属性

private:
    // 核心组件 - 简化管理
    // QScopedPointer<WhiteBoardWidget> m_view;       // 视图组件使用智能指针
    WhiteBoardWidget* m_view = nullptr; // 视图组件用裸指针，由Qt父子关系管理
    QPointer<WhiteBoardScene> m_scene;           // 场景使用Qt父子关系 + QPointer监控

    // 当前状态
    ToolType m_currentTool;
    QColor m_currentColor;
    qreal m_currentLineWidth;
    Qt::PenStyle m_currentPenStyle;
    PenType m_currentPenType;       // 新增：当前画笔类型
    qreal m_currentOpacity;
    QColor m_currentFillColor;
    bool m_currentFilled;



    // 4K屏幕适配
    qreal m_dpiScale;

    // 图片工具默认参数
    qreal m_defaultImageWidth;
    qreal m_defaultImageMaxHeight;

    // 初始化状态
    bool m_initialized;

    // 序列化和导出组件
    QScopedPointer<SceneSerializer> m_sceneSerializer;
    QScopedPointer<ImageExporter> m_imageExporter;
};

#endif // WHITEBOARD_H
