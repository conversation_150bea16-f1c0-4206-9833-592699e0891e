#ifndef TOOLBOXPOPUP_H
#define TOOLBOXPOPUP_H

#include <QWidget>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QScrollArea>
#include <QSvgWidget>
#include <QSvgRenderer>
#include <QPaintEvent>
#include <QPainter>
#include <QPainterPath>
#include "SideBarConstants.h"

/**
 * @brief 工具箱弹窗组件
 *
 * 作为WhiteboardView子组件的工具箱弹窗，显示通用工具列表
 * 特性：
 * - 半透明黑色背景 rgba(0,0,0,0.65)
 * - 40px圆角
 * - 网格布局显示工具按钮
 * - 支持点击外部关闭
 * - 作为父组件的子控件，不是独立窗口
 */
class ToolboxPopup : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param toolsConfig 工具配置列表
     * @param parent 父组件
     */
    explicit ToolboxPopup(const QList<SideBarConstants::ToolInfo>& toolsConfig, QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ToolboxPopup();
    
    /**
     * @brief 显示弹窗在指定位置
     * @param position 全局位置
     */
    void showAt(const QPoint& position);
    
    /**
     * @brief 隐藏弹窗
     */
    void hidePopup();

    /**
     * @brief 设置工具配置
     * @param toolsConfig 工具配置
     */
    void setToolsConfig(const QList<SideBarConstants::ToolInfo>& toolsConfig);

signals:
    /**
     * @brief 工具按钮点击信号
     * @param toolInfo 工具信息
     */
    void toolClicked(const SideBarConstants::ToolInfo& toolInfo);
    
    /**
     * @brief 弹窗关闭信号
     */
    void popupClosed();

protected:
    /**
     * @brief 绘制事件
     */
    void paintEvent(QPaintEvent *event) override;
    
    /**
     * @brief 鼠标按下事件
     */
    void mousePressEvent(QMouseEvent *event) override;
    
    /**
     * @brief 事件过滤器
     */
    bool eventFilter(QObject *obj, QEvent *event) override;

private:
    /**
     * @brief 初始化UI
     */
    void initializeUI();
    
    /**
     * @brief 根据工具配置创建弹窗内容
     * @param toolsConfig 工具配置列表
     */
    void createContentFromConfig(const QList<SideBarConstants::ToolInfo>& toolsConfig);

    /**
     * @brief 创建功能按钮
     * @param iconPath 图标路径
     * @param text 按钮文字
     * @param toolInfo 工具信息（用于点击事件识别）
     * @return 创建的按钮组件
     */
    QWidget* createFunctionButton(const QString& iconPath, const QString& text, const SideBarConstants::ToolInfo& toolInfo = SideBarConstants::ToolInfo());

    /**
     * @brief 创建图标组件（SVG或占位符）
     * @param iconPath 图标路径
     * @param size 图标尺寸
     * @param parent 父组件
     * @return 创建的图标组件
     */
    QWidget* createIconWidget(const QString& iconPath, const QSize& size, QWidget* parent);

    /**
     * @brief 创建标题标签
     * @param title 标题文本
     * @return 创建的标题标签
     */
    QLabel* createTitleLabel(const QString& title);

    /**
     * @brief 创建工具网格
     * @param toolsConfig 工具配置列表
     * @return 创建的工具网格组件
     */
    QWidget* createToolsGrid(const QList<SideBarConstants::ToolInfo>& toolsConfig);

    /**
     * @brief 创建空状态标签
     * @return 创建的空状态标签
     */
    QLabel* createEmptyLabel();

    /**
     * @brief 清除现有内容
     */
    void clearExistingContent();

    /**
     * @brief 创建内容组件
     */
    void createContentWidget();

    /**
     * @brief 调整位置以适应屏幕边界
     */
    void adjustPositionToScreen();

    /**
     * @brief 调整位置以适应父组件边界
     */
    void adjustPositionToParent();

    /**
     * @brief 处理工具按钮点击
     * @param clickedWidget 被点击的组件
     * @return 是否处理了点击事件
     */
    bool handleToolButtonClick(QWidget* clickedWidget);

    /**
     * @brief 处理外部点击
     * @param globalPos 全局点击位置
     * @return 是否处理了外部点击
     */
    bool handleOutsideClick(const QPoint& globalPos);

    
    /**
     * @brief 安装全局事件过滤器
     */
    void installGlobalEventFilter();
    
    /**
     * @brief 移除全局事件过滤器
     */
    void removeGlobalEventFilter();

private:
    QVBoxLayout* m_mainLayout;              ///< 主布局
    QWidget* m_contentWidget;               ///< 内容组件

    bool m_globalFilterInstalled;           ///< 是否已安装全局事件过滤器
    bool m_isClosing;                       ///< 是否正在关闭（防止递归调用）

    QList<SideBarConstants::ToolInfo> m_toolsConfig;  ///< 工具配置列表
};

#endif // TOOLBOXPOPUP_H
