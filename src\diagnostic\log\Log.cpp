﻿//
// Created by HLJY on 2025/6/26.
//

#include "Log.h"
#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/daily_file_sink.h"
#include "src/constants/AppConstants.h"

#include <iostream>
#include <QString>
#include <QCoreApplication>
#include <QDebug>
#include <QStandardPaths>

void Log::init() {
    QString T_MESSAGE_PATTERN = "%{time yyyy-MM-dd hh:mm:ss.zzz} %{appname} %{type} [%{function}:%{line}] %{pid}:%{threadid} - %{message}";
    qSetMessagePattern(T_MESSAGE_PATTERN);


    std::vector<spdlog::sink_ptr> sinks;

    std::string spdlog_pattern = "%v";
    auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    console_sink->set_level(toSpdlogLevel(m_logLevel));
    console_sink->set_pattern(spdlog_pattern);
    sinks.push_back(console_sink);

    QString logPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +  "/" + AppConstants::APP_NAME + "/logs/hl-whiteboard-qt.log";
    // 转换为std::string
    std::string logFileStdString = logPath.toStdString();
    std::cout << "logPath: " << logFileStdString;
    // 创建文件sink
    auto file_sink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(logFileStdString, 00, 00, false, 14);
    file_sink->set_level(toSpdlogLevel(m_logLevel));
    file_sink->set_pattern(spdlog_pattern);
    sinks.push_back(file_sink);

    m_logger = std::make_shared<spdlog::logger>("global", begin(sinks), end(sinks));
    m_logger->set_level(toSpdlogLevel(m_logLevel));
    m_logger->flush_on(spdlog::level::warn);  // 当 WARN 及以上级别日志出现时立即刷新缓冲区

    // 每3刷新一次日志缓冲区
    spdlog::flush_every(std::chrono::seconds(3));

    qInstallMessageHandler(Log::messageHandler);

    qDebug() << "日志初始化完成，日志位置：" << logPath;

}

void Log::messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg) {

    auto formatted = qFormatLogMessage(type, context, msg);

    m_logger->log(toSpdlogLevel(type), formatted.toStdString());
}

void Log::setLogLevel(QtMsgType level) {
    m_logLevel = level;
}

spdlog::level::level_enum Log::toSpdlogLevel(QtMsgType qtLevel) {
    switch (qtLevel) {
        case QtDebugMsg:
            return spdlog::level::debug;
        case QtInfoMsg:
            return spdlog::level::info;
        case QtWarningMsg:
            return spdlog::level::warn;
        case QtCriticalMsg:
            return spdlog::level::err;
        case QtFatalMsg:
            return spdlog::level::critical;
        default:
            return spdlog::level::debug;
    }
}

void Log::flushOnExit(QCoreApplication *app) {
    QObject::connect(app, &QCoreApplication::aboutToQuit, []{
        qDebug() << "应用程序即将退出，刷新日志...";
        m_logger->flush();
        spdlog::shutdown();
    });
}
