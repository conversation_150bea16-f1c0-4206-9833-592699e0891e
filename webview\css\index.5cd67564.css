.timer-text[data-v-a8673ac9] {
  width: 3.5rem;
  height: 7rem;
  background: #ffffff;
  border-radius: 0.75rem;
  font-family: var(--font-family-number);
  font-weight: 900;
  font-size: 5.33333rem;
  line-height: 7rem;
  color: #000000;
  text-align: center;
}
.timer-hour[data-v-a8673ac9],
.timer-minute[data-v-a8673ac9],
.timer-second[data-v-a8673ac9] {
  gap: 0.75rem;
}
.timer-controller[data-v-a8673ac9] {
  gap: 3.16667rem;
  margin-top: 1.16667rem;
}
.timer-button[data-v-a8673ac9] {
  width: 4.66667rem;
  height: 3.66667rem;
}
.count-dot-colon[data-v-a8673ac9] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 2rem;
  margin: 0 0.75rem;
}
.dot[data-v-a8673ac9] {
  width: 0.66667rem;
  height: 0.66667rem;
  border-radius: 50%;
  background: #fff;
}
.timer-title[data-v-a8673ac9] {
  font-family: var(--font-family-title);
  font-size: 2.16667rem;
  color: var(--color-white-light-65);
  text-align: center;
  margin-bottom: 5rem;
}
.fullscreen .timer-text[data-v-a8673ac9] {
  width: auto;
  background: none;
  border-radius: 0;
  font-weight: bold;
  font-size: 12.5rem;
  color: #ffffff;
  height: 12.5rem;
  line-height: 12.5rem;
}
.fullscreen .count-dot-colon[data-v-a8673ac9] {
  height: 5.83333rem;
  margin: 0 4.66667rem;
}
.fullscreen .dot[data-v-a8673ac9] {
  width: 1.83333rem;
  height: 1.83333rem;
  border-radius: 50%;
  background: #fff;
}
.fullscreen .timer-controller[data-v-a8673ac9] {
  margin-top: 4.66667rem;
}
.fullscreen .timer-button[data-v-a8673ac9] {
  width: 7.16667rem;
  height: 5.66667rem;
}
.fullscreen .timer-controller[data-v-a8673ac9] {
  gap: 4.75rem;
}
.count-setting-container[data-v-570b83cf] {
  height: 7rem;
  width: 17.83333rem;
  border-radius: 0.75rem;
}
.count-common-setting[data-v-570b83cf] {
  height: 1.91667rem;
  font-size: 0.91667rem;
  color: #ffffff;
  margin-bottom: 0.5rem;
  line-height: 1.91667rem;
  justify-content: space-between;
}
.count-common-setting .shortcut-key[data-v-570b83cf] {
  width: 5.33333rem;
  height: 1.91667rem;
  background: #5f52e3;
  border-radius: 3.91667rem;
  text-align: center;
}
.count-setting-body[data-v-570b83cf] {
  height: 5.16667rem;
  border-radius: 0.75rem;
  background: #ffffff;
  padding: 0.33333rem 0.75rem;
  box-sizing: border-box;
}
.count-unit[data-v-570b83cf] {
  font-weight: 600;
  font-size: 0.91667rem;
  color: rgba(0, 0, 0, 0.65);
}
.timer-controller[data-v-570b83cf] {
  gap: 3.16667rem;
  margin-top: 1.16667rem;
}
.timer-button[data-v-570b83cf] {
  width: 4.66667rem;
  height: 3.66667rem;
}
.count-setting-item[data-v-570b83cf] {
  height: 100%;
  width: 4.16667rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.operator-button[data-v-570b83cf] {
  font-size: 0.66667rem;
  color: #5f52e3;
  width: 1.33333rem;
  text-align: center;
  height: 1.33333rem;
  position: absolute;
}
.operator-button[data-v-570b83cf]:first-child {
  top: -0.33333rem;
}
.operator-button[data-v-570b83cf]:last-child {
  bottom: -0.66667rem;
}
.count-number[data-v-570b83cf] {
  font-family: var(--font-family-number);
  font-weight: bold;
  font-size: 3.75rem;
  color: #000000;
  line-height: 3.25rem;
  letter-spacing: 0;
}
.count-running-container[data-v-570b83cf] {
  height: 7rem;
  width: 17.83333rem;
  background: #000000;
  border-radius: 0.75rem;
  color: #ffffff;
  font-weight: bold;
  font-size: 5.33333rem;
  gap: 0.75rem;
}
.count-running-container .font-number[data-v-570b83cf] {
  font-family: var(--font-family-number);
}
.count-running-container.show-hour[data-v-570b83cf] {
  font-size: 3.75rem;
}
.count-dot-colon[data-v-570b83cf] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 2rem;
}
.dot[data-v-570b83cf] {
  width: 0.66667rem;
  height: 0.66667rem;
  border-radius: 50%;
  background: #fff;
}
.count-title[data-v-570b83cf] {
  font-family: var(--font-family-title);
  font-size: 2.16667rem;
  color: var(--color-white-light-65);
  text-align: center;
  margin-bottom: 5rem;
}
.fullscreen .count-running-container[data-v-570b83cf] {
  height: 12.5rem;
  gap: 0;
}
.fullscreen .font-number[data-v-570b83cf] {
  width: auto;
  background: none;
  border-radius: 0;
  font-weight: bold;
  font-size: 12.5rem;
  color: #ffffff;
  height: 12.5rem;
  line-height: 12.5rem;
}
.fullscreen .timer-controller[data-v-570b83cf] {
  margin-top: 4.66667rem;
}
.fullscreen .count-dot-colon[data-v-570b83cf] {
  height: 5.83333rem;
  margin: 0 4.66667rem;
}
.fullscreen .dot[data-v-570b83cf] {
  width: 1.83333rem;
  height: 1.83333rem;
  border-radius: 50%;
  background: #fff;
}
.fullscreen .timer-button[data-v-570b83cf] {
  width: 7.16667rem;
  height: 5.66667rem;
}
.fullscreen .timer-controller[data-v-570b83cf] {
  gap: 4.75rem;
}
.tick-tock-container[data-v-6c36e769] {
  position: relative;
  background: linear-gradient(180deg, #956ef7 0%, #6053e3 100%);
  border-radius: 3.33333rem;
  padding: 1.58333rem;
  padding-top: 3.16667rem;
  min-width: 20.91667rem;
  box-sizing: border-box;
  width: 20.91667rem;
  overflow: hidden;
}
.tick-tock-tabs[data-v-6c36e769] {
  font-family: var(--font-family-title);
  font-size: 1.25rem;
  color: var(--color-white-light-65);
  line-height: 1.25rem;
  gap: 4.66667rem;
  margin-bottom: 1.75rem;
  position: relative;
  width: 17.83333rem;
}
.tick-tock-tabs .active[data-v-6c36e769] {
  color: #ffffff;
}
.tick-tock-tabs_under[data-v-6c36e769] {
  position: absolute;
  bottom: -1rem;
  left: 0;
  color: #29da80;
  transition: transform 0.3s ease;
  pointer-events: none;
}
.dialog-close-box[data-v-6c36e769] {
  width: 100%;
  position: absolute;
  bottom: -3.16667rem;
  left: 0;
  gap: 3.33333rem;
}
.dialog-close[data-v-6c36e769] {
  width: 1.91667rem;
  height: 1.91667rem;
  background: #000000;
  border-radius: 0.41667rem;
  font-size: 1.66667rem;
  color: #ffffff;
}
.dialog-close img[data-v-6c36e769] {
  width: 1.66667rem;
  height: 1.66667rem;
}
.fullscreen[data-v-6c36e769] {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  border-radius: 0;
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.fullscreen .fullscreen-exit[data-v-6c36e769] {
  background: rgba(255, 255, 255, 0.25);
  border-radius: 3.91667rem;
  color: #ffffff;
  font-size: 1.75rem;
  padding: 1rem 2.5rem;
  bottom: 4.58333rem;
  position: absolute;
}
