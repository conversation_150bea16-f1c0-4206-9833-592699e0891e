#ifndef DRAWINGCONSTANTS_H
#define DRAWINGCONSTANTS_H

#include <QVector>
#include <QPainterPath>

/**
 * @brief 绘制相关常量定义
 */
namespace DrawingConstants {

    // ========== 虚线模式常量 ==========

    /**
     * @brief 自定义虚线模式 - 4:6比例
     * 4个单位实线，6个单位空隙
     */
    const QVector<qreal> CUSTOM_DASH_PATTERN = {4.0, 6.0};

    // ========== 全局擦除区域管理 ==========

    /**
     * @brief 当前正在绘制的橡皮擦路径（用于视觉反馈）
     */
    extern QPainterPath g_currentEraserPath;

    /**
     * @brief 未完成的切割路径数组（等待处理的切割操作）
     */
    extern QList<QPainterPath> g_clipPaths;

    /**
     * @brief 添加擦除区域到当前路径
     */
    void addEraserRect(const QRectF& rect);

    /**
     * @brief 获取当前擦除路径
     */
    QPainterPath getCurrentEraserPath();

    /**
     * @brief 获取切割路径数组
     */
    QList<QPainterPath> getClipPaths();

    /**
     * @brief 取出并删除切割数组中的首条数据
     * @return 首条路径，如果数组为空则返回空路径
     */
    QPainterPath takeFirstClipPath();

    /**
     * @brief 检查切割数组是否为空
     */
    bool hasClipPaths();

    /**
     * @brief 是否清除完毕
     */
    void setDrawClear(bool clear);

    /**
     * @brief 清空所有擦除数据
     */
    void clearAllEraserData();

} // namespace DrawingConstants

#endif // DRAWINGCONSTANTS_H
