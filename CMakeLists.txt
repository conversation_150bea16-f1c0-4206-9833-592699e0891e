cmake_minimum_required(VERSION 3.5)

project(hl-whiteboard-qt LANGUAGES CXX VERSION 1.0.0)

set(CMAKE_INCLUDE_CURRENT_DIR ON)


set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译器优化配置
if(MSVC)
    # MSVC编译器优化 - 启用激进优化提升绘制性能
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /Ob2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")

    # 启用快速浮点运算（提升数学计算性能）
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /fp:fast")

    # 启用SIMD指令集优化（针对路径计算）
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /arch:AVX2")

    # 启用内联函数优化
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Oi")

    # 启用函数级链接优化
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Gy")

    # 启用链接时优化 - 对绘制性能有显著提升
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /GL")
    set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /LTCG")

    # 启用向量化和并行化优化（针对绘制计算）
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Qvec")

    # 启用激进内联和循环展开优化
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Ob3")

else()
    # GCC/MinGW编译器优化
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
    # 启用快速数学运算
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ffast-math")
    # 启用向量化优化
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -ftree-vectorize")
    # 启用SIMD指令集
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -mavx2")
endif()

# 启用Qt自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 启用预编译头文件支持
set(CMAKE_AUTOMOC_MACRO_NAMES "Q_OBJECT;Q_GADGET;Q_NAMESPACE")

# 添加全局预编译头文件
set(GLOBAL_HEADER "${CMAKE_SOURCE_DIR}/src/global.h")
add_compile_definitions(GLOBAL_HEADER_INCLUDED=1)

# 开启对Qt的日志上下文支持，这样在release模式下可以打印出日志上下文（文件名行号等）
add_definitions(-DQT_MESSAGELOGCONTEXT)

# 设置默认Qt版本
set(QT_VERSION_MAJOR 6)

# 添加zmq相关头文件
include_directories(
        3rd/win/cppzmq/include
        3rd/win/libzmq/include
        3rd/win/nlohmann/include
        3rd/win/BS_thread_pool/include
        3rd/win/cpptime/include
)


# 添加spdlog头文件
include_directories(
        3rd/win/spdlog/include
)

if(WIN32)
    set(ZMQ_ROOT "${CMAKE_SOURCE_DIR}/3rd/win/libzmq")
else()
    message(FATAL_ERROR "zmq is only supported on Windows platforms")
endif()



# 链接zmq静态库
if(CMAKE_BUILD_TYPE MATCHES "Debug" OR
(CMAKE_CONFIGURATION_TYPES AND "Debug" IN_LIST CMAKE_CONFIGURATION_TYPES))
    set(ZMQ_LIB_DIR "${ZMQ_ROOT}/Debug")
    set(ZMQ_BUILD_TYPE "Debug")
    set(ZMQ_LIB_NAME "libzmq-v143-mt-gd-4_3_5")
else()
    set(ZMQ_LIB_DIR "${ZMQ_ROOT}/Release")
    set(ZMQ_BUILD_TYPE "Release")
    set(ZMQ_LIB_NAME "libzmq-v143-mt-4_3_5")
endif()

# 添加zmq相关库目录
link_directories(
        "${ZMQ_LIB_DIR}/lib"
)


# 平台检测
if(WIN32)
    set(QCEFVIEW_ROOT "${CMAKE_SOURCE_DIR}/3rd/win/QCefView")
elseif(APPLE)
    set(QCEFVIEW_ROOT "${CMAKE_SOURCE_DIR}/3rd/mac/QCefView")
else()
    message(FATAL_ERROR "QCefView is only supported on Windows and macOS platforms")
endif()



# 检查QCefView目录是否存在
if(NOT EXISTS "${QCEFVIEW_ROOT}")
    message(FATAL_ERROR "QCefView directory not found: ${QCEFVIEW_ROOT}")
endif()

if(CMAKE_BUILD_TYPE MATCHES "Debug" OR
   (CMAKE_CONFIGURATION_TYPES AND "Debug" IN_LIST CMAKE_CONFIGURATION_TYPES))
    set(QCEFVIEW_LIB_DIR "${QCEFVIEW_ROOT}/Debug")
    set(QCEFVIEW_BUILD_TYPE "Debug")
else()
    set(QCEFVIEW_LIB_DIR "${QCEFVIEW_ROOT}/Release")
    set(QCEFVIEW_BUILD_TYPE "Release")
endif()

# 检查库文件是否存在
if(NOT EXISTS "${QCEFVIEW_LIB_DIR}/lib")
    message(FATAL_ERROR "QCefView library directory not found: ${QCEFVIEW_LIB_DIR}/lib")
endif()

# 添加包含路径和库路径
include_directories("${QCEFVIEW_ROOT}/include")
link_directories("${QCEFVIEW_LIB_DIR}/lib")


# 尝试查找Qt
if (QT_VERSION_MAJOR EQUAL 6)
    find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Widgets Core Gui Svg SvgWidgets Qml Quick Xml Network OpenGL OpenGLWidgets Concurrent QUIET)
else()
    find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Widgets Core Gui Svg Qml Quick QuickControls2 Network QUIET)
endif()

if (NOT Qt${QT_VERSION_MAJOR}_FOUND)
    # 如果找不到指定版本，切换尝试另一个版本
    if (QT_VERSION_MAJOR EQUAL 5)
        set(QT_VERSION_MAJOR 6)
        find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Widgets Core Gui Svg SvgWidgets Qml Quick Xml Network OpenGL OpenGLWidgets Concurrent QUIET)
    else()
        set(QT_VERSION_MAJOR 5)
        find_package(Qt${QT_VERSION_MAJOR} COMPONENTS Widgets Core Gui Svg Qml Quick QuickControls2 Network QUIET)
    endif()
endif()

if (NOT Qt${QT_VERSION_MAJOR}_FOUND)
    message(WARNING "Qt not found automatically. Please specify Qt location manually.")
    # 不立即退出，而是给用户一个机会手动配置
endif()

message(STATUS "Using Qt version: ${QT_VERSION_MAJOR}")

# 添加包含目录
include_directories(${CMAKE_SOURCE_DIR})

# 资源文件
set(RESOURCES
    resources/resources.qrc
)

# 自动查找根目录及子目录下的所有源文件
file(GLOB_RECURSE ALL_SOURCES
    "src/components/*.cpp"
    "src/components/*.h"
    "src/core/*.cpp"
    "src/core/*.h"
    "src/diagnostic/*.cpp"
    "src/diagnostic/*.h"
    "src/interface/*.cpp"
    "src/interface/*.h"
    "src/jsbridge_handler/*.cpp"
    "src/jsbridge_handler/*.h"
    "src/models/*.cpp"
    "src/models/*.h"
    "src/screen_adaptation/*.cpp"
    "src/screen_adaptation/*.h"
    "src/services/*.cpp"
    "src/services/*.h"
    "src/utils/*.cpp"
    "src/utils/*.h"
    "src/zmq_handler/*.cpp"
    "src/zmq_handler/*.h"
    "src/global.h"
    
        "main.cpp"
        "mainwindow.cpp"
        "mainwindow.h"
        "mainwindow.ui"
)

# 添加whiteboard子模块
add_subdirectory(src/whiteboard)

# 排除 build 目录中的文件
list(FILTER ALL_SOURCES EXCLUDE REGEX "build/.*")

# 全局头文件
set(GLOBAL_SOURCES
    ${CMAKE_SOURCE_DIR}/src/global.h
)

# 合并所有源文件
set(SOURCES
    ${ALL_SOURCES}
    ${GLOBAL_SOURCES}
)

# 头文件安装目录
set(HEADERS_INSTALL_DIR ${CMAKE_INSTALL_PREFIX}/include/${PROJECT_NAME})

# 设置 Qt MOC 忽略 3rd 目录下的所有头文件
file(GLOB_RECURSE THIRD_PARTY_HEADERS "${CMAKE_SOURCE_DIR}/3rd/**/*.h")
foreach(HEADER ${THIRD_PARTY_HEADERS})
    set_property(SOURCE "${HEADER}" PROPERTY SKIP_AUTOMOC ON)
endforeach()

message(STATUS "Skipping AUTOMOC for ${CMAKE_CURRENT_LIST_DIR}/3rd directory headers")

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${RESOURCES})

# 配置预编译头文件
target_precompile_headers(${PROJECT_NAME} PRIVATE ${GLOBAL_HEADER})

# 为所有源文件添加全局头文件包含
target_compile_options(${PROJECT_NAME} PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:/FI${GLOBAL_HEADER}>
    $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-include ${GLOBAL_HEADER}>
)

# 链接Qt库和QCefView
if (QT_VERSION_MAJOR EQUAL 6)
    target_link_libraries(${PROJECT_NAME} PRIVATE
        Qt${QT_VERSION_MAJOR}::Widgets
        Qt${QT_VERSION_MAJOR}::Core
        Qt${QT_VERSION_MAJOR}::Gui
        Qt${QT_VERSION_MAJOR}::Svg
        Qt${QT_VERSION_MAJOR}::SvgWidgets
        Qt${QT_VERSION_MAJOR}::Qml
        Qt${QT_VERSION_MAJOR}::Quick
        Qt${QT_VERSION_MAJOR}::Xml
        Qt${QT_VERSION_MAJOR}::Network
        Qt${QT_VERSION_MAJOR}::OpenGL
        Qt${QT_VERSION_MAJOR}::OpenGLWidgets
        QCefView
        whiteboard
            # zmq
            ${ZMQ_LIB_NAME}
    )
else()
    target_link_libraries(${PROJECT_NAME} PRIVATE
        Qt${QT_VERSION_MAJOR}::Widgets
        Qt${QT_VERSION_MAJOR}::Core
        Qt${QT_VERSION_MAJOR}::Gui
        Qt${QT_VERSION_MAJOR}::Svg
        Qt${QT_VERSION_MAJOR}::Qml
        Qt${QT_VERSION_MAJOR}::Quick
        Qt${QT_VERSION_MAJOR}::QuickControls2
        Qt${QT_VERSION_MAJOR}::Xml
        Qt${QT_VERSION_MAJOR}::Network
        QCefView
        whiteboard
            # zmq
            ${ZMQ_LIB_NAME}
    )
endif()

message(STATUS "QCefView library linked from: ${QCEFVIEW_LIB_DIR}/lib/QCefView.lib")

# 添加QCefView相关的编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    QCEFVIEW_IMPORT
)

# 添加编译器特定的性能优化定义
if(MSVC)
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        COMPILER_MSVC=1
        _CRT_SECURE_NO_WARNINGS
        # 启用数学优化宏定义
        _USE_MATH_DEFINES
        # 在Release模式下禁用安全检查以提升性能
        $<$<CONFIG:Release>:_SECURE_SCL=0>
        $<$<CONFIG:Release>:_HAS_ITERATOR_DEBUGGING=0>
        # 启用快速字符串处理
        $<$<CONFIG:Release>:_CRT_DISABLE_PERFCRIT_LOCKS>
    )
    # MSVC特定的性能优化选项
    target_compile_options(${PROJECT_NAME} PRIVATE
        /bigobj  # 支持大对象文件
        /MP      # 多处理器编译
        # 针对Intel 64位优化（提升绘制计算性能）
        $<$<CONFIG:Release>:/favor:INTEL64>
        # 在Release模式下禁用Spectre缓解以提升性能
        $<$<CONFIG:Release>:/Qspectre->
        # 启用函数内联优化
        $<$<CONFIG:Release>:/Ob3>
    )
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        COMPILER_GCC=1
    )
    # GCC/MinGW特定的性能优化选项
    target_compile_options(${PROJECT_NAME} PRIVATE
        -march=native  # 针对当前CPU优化
        -mtune=native  # 针对当前CPU调优
    )
endif()

# 复制webview目录到输出目录
if(EXISTS "${CMAKE_SOURCE_DIR}/webview")
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            "${CMAKE_SOURCE_DIR}/webview"
            "$<TARGET_FILE_DIR:${PROJECT_NAME}>/webview"
        COMMENT "Copying webview directory to output directory"
    )
    message(STATUS "Webview directory will be copied from: ${CMAKE_SOURCE_DIR}/webview")
else()
    message(WARNING "Webview directory not found: ${CMAKE_SOURCE_DIR}/webview")
endif()

# 复制QCefView的依赖文件到输出目录
# 检查CEF依赖文件目录是否存在
set(QCEFVIEW_BIN_DIR "${QCEFVIEW_LIB_DIR}/bin")
if(EXISTS "${QCEFVIEW_BIN_DIR}/CefView")
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        "${QCEFVIEW_BIN_DIR}/CefView"
        "$<TARGET_FILE_DIR:${PROJECT_NAME}>/CefView"
        COMMENT "Copying QCefView dependencies (${QCEFVIEW_BUILD_TYPE})"
    )
    message(STATUS "QCefView dependencies will be copied from: ${QCEFVIEW_BIN_DIR}/CefView")
else()
    message(WARNING "QCefView dependencies directory not found: ${QCEFVIEW_BIN_DIR}/CefView")
endif()

# 复制QCefView DLL文件
if(EXISTS "${QCEFVIEW_BIN_DIR}/QCefView.dll")
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${QCEFVIEW_BIN_DIR}/QCefView.dll"
        "$<TARGET_FILE_DIR:${PROJECT_NAME}>"
        COMMENT "Copying QCefView.dll"
    )
else()
    message(WARNING "QCefView.dll not found: ${QCEFVIEW_BIN_DIR}/QCefView.dll")
endif()

# 复制zmq动态库到可执行文件目录（仅文件不存在时复制）
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${ZMQ_LIB_DIR}/bin/${ZMQ_LIB_NAME}.dll
        $<TARGET_FILE_DIR:${PROJECT_NAME}>)

# 设置目标属性 - 根据构建类型设置WIN32_EXECUTABLE
if(CMAKE_BUILD_TYPE MATCHES "Debug")
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE FALSE
    )
else()
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()

# Qt6 部署配置
if (QT_VERSION_MAJOR EQUAL 6 AND WIN32)
    # 设置QML导入路径
    set_target_properties(${PROJECT_NAME} PROPERTIES
        QT_QML_ROOT_PATH "${CMAKE_SOURCE_DIR}/resources/qml"
    )

    # 查找windeployqt工具
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)

    if(WINDEPLOYQT_EXECUTABLE)
        # 使用windeployqt自动部署Qt依赖（仅复制文件，不运行程序）
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${WINDEPLOYQT_EXECUTABLE} --no-translations --no-system-d3d-compiler --no-opengl-sw --qmldir "${CMAKE_SOURCE_DIR}/resources/qml" $<TARGET_FILE:${PROJECT_NAME}>
            COMMENT "Deploying Qt libraries with windeployqt"
            VERBATIM
        )
        message(STATUS "windeployqt found: ${WINDEPLOYQT_EXECUTABLE}")
    else()
        message(WARNING "windeployqt not found. You may need to manually copy Qt DLLs.")

        # 手动复制关键的Qt DLL文件
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${Qt6_DIR}/../../../bin/Qt6Core.dll"
                "${Qt6_DIR}/../../../bin/Qt6Gui.dll"
                "${Qt6_DIR}/../../../bin/Qt6Widgets.dll"
                "${Qt6_DIR}/../../../bin/Qt6Svg.dll"
                "${Qt6_DIR}/../../../bin/Qt6Qml.dll"
                "${Qt6_DIR}/../../../bin/Qt6Quick.dll"
                "${Qt6_DIR}/../../../bin/Qt6Xml.dll"
                $<TARGET_FILE_DIR:${PROJECT_NAME}>
            COMMENT "Manually copying Qt DLLs"
            VERBATIM
        )
    endif()
endif()

# 安装目标
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

add_subdirectory(${PROJECT_SOURCE_DIR}/examples/zmq)
add_subdirectory(${PROJECT_SOURCE_DIR}/examples/jsbridge)

