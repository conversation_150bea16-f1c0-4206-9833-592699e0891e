#include "ToolboxPopup.h"
#include <QApplication>
#include <QScreen>
#include <QMouseEvent>
#include <QDebug>
#include <QSvgWidget>
#include <QSvgRenderer>

ToolboxPopup::ToolboxPopup(const QList<SideBarConstants::ToolInfo>& toolsConfig, QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_contentWidget(nullptr)
    , m_globalFilterInstalled(false)
    , m_isClosing(false)
    , m_toolsConfig(toolsConfig)
{
    setObjectName("ToolboxPopup");

    // 作为子组件，不需要设置窗口标志
    setAttribute(Qt::WA_TranslucentBackground);

    // 设置固定大小
    setFixedSize(SideBarConstants::getToolboxPopupWidth(), SideBarConstants::getToolboxPopupHeight());

    // 初始状态为隐藏
    hide();

    initializeUI();

    qDebug() << "ToolboxPopup: 创建工具箱弹窗组件";
}

ToolboxPopup::~ToolboxPopup()
{
    removeGlobalEventFilter();
    qDebug() << "ToolboxPopup: 销毁工具箱弹窗组件";
}

void ToolboxPopup::showAt(const QPoint& position)
{
    // 重置关闭标志
    m_isClosing = false;

    // 设置位置（相对于父组件）
    QPoint localPos = position;
    if (parentWidget()) {
        localPos = parentWidget()->mapFromGlobal(position);
    }
    move(localPos);

    // 确保不会超出父组件边界
    adjustPositionToParent();

    // 显示弹窗
    show();
    raise();

    // 安装全局事件过滤器
    installGlobalEventFilter();

    qDebug() << "ToolboxPopup: 显示弹窗在位置:" << geometry();
}

void ToolboxPopup::hidePopup()
{
    // 防止递归调用
    if (m_isClosing) {
        qDebug() << "ToolboxPopup: 弹窗已在关闭过程中，忽略重复调用";
        return;
    }

    m_isClosing = true;

    hide();
    removeGlobalEventFilter();
    emit popupClosed();

    m_isClosing = false;

    qDebug() << "ToolboxPopup: 隐藏弹窗";
}

void ToolboxPopup::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 清除背景
    painter.fillRect(rect(), Qt::transparent);

    // 绘制圆角背景
    QPainterPath path;
    int borderRadius = SideBarConstants::getToolboxPopupBorderRadius();
    path.addRoundedRect(rect(), borderRadius, borderRadius);

    // 设置背景色 rgba(0,0,0,0.65)
    QColor backgroundColor(0, 0, 0, 166); // 0.65 * 255 = 166
    painter.fillPath(path, backgroundColor);
}

void ToolboxPopup::mousePressEvent(QMouseEvent *event)
{
    // 阻止事件传播，避免点击弹窗内部时关闭
    event->accept();
}

bool ToolboxPopup::handleToolButtonClick(QWidget* clickedWidget)
{
    while (clickedWidget) {
        if (clickedWidget && clickedWidget->property("toolInfo").isValid()) {
            QVariant toolInfoVariant = clickedWidget->property("toolInfo");
            SideBarConstants::ToolInfo toolInfo = toolInfoVariant.value<SideBarConstants::ToolInfo>();

            emit toolClicked(toolInfo);
            qDebug() << "ToolboxPopup: 工具按钮点击:" << toolInfo.toolName;
            hidePopup(); // 点击工具后关闭弹窗
            return true;
        }
        clickedWidget = clickedWidget->parentWidget();
    }
    return false;
}

bool ToolboxPopup::handleOutsideClick(const QPoint& globalPos)
{
    // 将全局坐标转换为相对于父组件的坐标
    QPoint localPos = globalPos;
    if (parentWidget()) {
        localPos = parentWidget()->mapFromGlobal(globalPos);
    }

    QRect popupRect = geometry();
    bool clickedInside = popupRect.contains(localPos);

    qDebug() << "ToolboxPopup: 鼠标点击位置:" << globalPos << "本地位置:" << localPos << "弹窗区域:" << popupRect << "在内部:" << clickedInside;

    if (!clickedInside) {
        qDebug() << "ToolboxPopup: 点击在弹窗外部，关闭弹窗";
        hidePopup();
        return true; // 表示处理了外部点击
    }
    return false;
}

bool ToolboxPopup::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->button() == Qt::LeftButton) {
	        QWidget* clickedWidget = QApplication::widgetAt(mouseEvent->globalPosition().toPoint());

            // 检查是否点击了工具按钮
            if (handleToolButtonClick(clickedWidget)) {
                return true;
            }

            // 处理外部点击
            if (handleOutsideClick(mouseEvent->globalPos())) {
                return false; // 不拦截事件，让其他组件正常处理
            }
        }
    }

    return QWidget::eventFilter(obj, event);
}



void ToolboxPopup::initializeUI()
{
    m_mainLayout = new QVBoxLayout(this);
    int padding = SideBarConstants::getToolboxPopupPadding();
    m_mainLayout->setContentsMargins(padding, padding, padding, padding);
    m_mainLayout->setSpacing(0);

    // 根据工具配置创建内容
    createContentFromConfig(m_toolsConfig);

    m_mainLayout->addWidget(m_contentWidget);
}

void ToolboxPopup::setToolsConfig(const QList<SideBarConstants::ToolInfo>& toolsConfig) {
    createContentFromConfig(toolsConfig);
    m_mainLayout->addWidget(m_contentWidget);
}

void ToolboxPopup::clearExistingContent()
{
    if (m_contentWidget) {
        m_mainLayout->removeWidget(m_contentWidget);
        delete m_contentWidget;
        m_contentWidget = nullptr;
    }
}

void ToolboxPopup::createContentWidget()
{
    m_contentWidget = new QWidget();
    m_contentWidget->setAttribute(Qt::WA_TranslucentBackground);
    m_contentWidget->setStyleSheet(
        "QWidget {"
        "    background: transparent;"
        "    background-color: transparent;"
        "}"
    );
}

void ToolboxPopup::createContentFromConfig(const QList<SideBarConstants::ToolInfo>& toolsConfig)
{
    clearExistingContent();
    createContentWidget();

    QVBoxLayout* contentLayout = new QVBoxLayout(m_contentWidget);
    contentLayout->setContentsMargins(0, 0, 0, 0);
    contentLayout->setAlignment(Qt::AlignTop);
    contentLayout->setSpacing(0);

    // 添加分组标题
    QLabel* titleLabel = createTitleLabel("通用工具");
    contentLayout->addWidget(titleLabel);
    contentLayout->addSpacing(SideBarConstants::getToolboxPopupTitleSpacing());

    // 创建工具内容
    if (!toolsConfig.isEmpty()) {
        QWidget* toolsGrid = createToolsGrid(toolsConfig);
        contentLayout->addWidget(toolsGrid);
    } else {
        QLabel* emptyLabel = createEmptyLabel();
        contentLayout->addWidget(emptyLabel);
    }
}

QWidget* ToolboxPopup::createFunctionButton(const QString& iconPath, const QString& text, const SideBarConstants::ToolInfo& toolInfo)
{
    QWidget* container = new QWidget(this);
    // 使用功能按钮最大宽度限制
    int buttonWidth = SideBarConstants::getExpandedWidth();
    // 精确计算按钮高度：图标高度 + 文字高度 + 图标和文字之间的间距
    int iconSize = SideBarConstants::getFunctionButtonSize();
    int textSize = SideBarConstants::getFunctionButtonTextSize();
    int buttonHeight = iconSize + textSize + 4;

    container->setFixedSize(buttonWidth, buttonHeight);

    // 设置工具信息属性，用于点击事件识别
    if (!toolInfo.toolName.isEmpty()) {
        container->setProperty("toolInfo", QVariant::fromValue(toolInfo));
        container->installEventFilter(this);
    }

    QVBoxLayout* layout = new QVBoxLayout(container);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(4);
    layout->setAlignment(Qt::AlignTop | Qt::AlignHCenter);

    // 创建图标组件
    QSize iconSizeObj(SideBarConstants::getFunctionButtonSize(), SideBarConstants::getFunctionButtonSize());
    QWidget* iconWidget = createIconWidget(iconPath, iconSizeObj, container);
    layout->addWidget(iconWidget, 0, Qt::AlignHCenter);

    // 创建文本标签
    QLabel* textLabel = new QLabel(text, container);
    textLabel->setAlignment(Qt::AlignCenter);
    textLabel->setMaximumWidth(buttonWidth);
    textLabel->setWordWrap(false);
    textLabel->setStyleSheet(QString(
        "QLabel {"
        "    font-weight: 400;"
        "    font-size: %1px;"
        "    color: #FFFFFF;"
        "    line-height: 1.0;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(SideBarConstants::getFunctionButtonTextSize()));

    layout->addWidget(textLabel, 0, Qt::AlignHCenter);

    return container;
}

void ToolboxPopup::installGlobalEventFilter()
{
    if (!m_globalFilterInstalled) {
        QApplication::instance()->installEventFilter(this);
        m_globalFilterInstalled = true;
        qDebug() << "ToolboxPopup: 已安装全局事件过滤器";
    }
}

QWidget* ToolboxPopup::createIconWidget(const QString& iconPath, const QSize& size, QWidget* parent)
{
    QSvgWidget* svgWidget = new QSvgWidget(parent);
    svgWidget->setFixedSize(size);

    if (!iconPath.isEmpty()) {
        svgWidget->load(iconPath);
        if (svgWidget->renderer() && svgWidget->renderer()->isValid()) {
            return svgWidget;
        }
    }
    svgWidget->deleteLater();

    // 创建占位符
    QLabel* placeholderLabel = new QLabel("?", parent);
    placeholderLabel->setFixedSize(size);
    placeholderLabel->setAlignment(Qt::AlignCenter);
    placeholderLabel->setStyleSheet(QString(
        "QLabel { "
        "background-color: rgba(255, 0, 0, 0.3); "
        "color: white; "
        "font-size: %1px; "
        "border-radius: 4px; "
        "}"
    ).arg(qMin(size.width(), size.height()) / 3));

    return placeholderLabel;
}

QLabel* ToolboxPopup::createTitleLabel(const QString& title)
{
    QLabel* titleLabel = new QLabel(title);
    int titleFontSize = SideBarConstants::getFunctionButtonTextSize();
    titleLabel->setFixedHeight(titleFontSize + 2);
    titleLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    titleLabel->setContentsMargins(0, 0, 0, 0);
    titleLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    titleLabel->setStyleSheet(QString(
        "QLabel {"
        "    color: #FFFFFF;"
        "    font-size: %1px;"
        "    font-weight: 600;"
        "    background: transparent;"
        "    background-color: transparent;"
        "    padding: 0px;"
        "    margin: 0px;"
        "    line-height: 1.0;"
        "    border: none;"
        "}"
    ).arg(titleFontSize));
    return titleLabel;
}

QWidget* ToolboxPopup::createToolsGrid(const QList<SideBarConstants::ToolInfo>& toolsConfig)
{
    QWidget* toolsGrid = new QWidget();
    toolsGrid->setAttribute(Qt::WA_TranslucentBackground);
    toolsGrid->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    toolsGrid->setStyleSheet(
        "QWidget {"
        "    background: transparent;"
        "    background-color: transparent;"
        "    border: none;"
        "}"
    );

    QGridLayout* gridLayout = new QGridLayout(toolsGrid);
    gridLayout->setContentsMargins(0, 0, 0, 0);
    gridLayout->setVerticalSpacing(0);
    gridLayout->setHorizontalSpacing(SideBarConstants::getSmallButtonSpacing());

    // 根据传入的工具配置创建按钮
    int col = 0;
    for (const auto& toolInfo : toolsConfig) {
        QWidget* toolButton = createFunctionButton(toolInfo.toolIcon, toolInfo.toolName, toolInfo);
        if (toolButton) {
            gridLayout->addWidget(toolButton, 0, col);
            col++;
        }
    }

    return toolsGrid;
}

QLabel* ToolboxPopup::createEmptyLabel()
{
    QLabel* emptyLabel = new QLabel("暂无可用工具");
    emptyLabel->setAlignment(Qt::AlignCenter);
    emptyLabel->setStyleSheet(QString(
        "QLabel {"
        "    color: #FFFFFF;"
        "    font-size: %1px;"
        "    background: transparent;"
        "}"
    ).arg(SideBarConstants::getFunctionButtonTextSize()));
    return emptyLabel;
}

void ToolboxPopup::adjustPositionToScreen()
{
    QScreen* screen = QApplication::primaryScreen();
    if (!screen) {
        return;
    }

    QRect screenGeometry = screen->availableGeometry();
    QRect popupGeometry = geometry();

    // 调整X坐标
    if (popupGeometry.right() > screenGeometry.right()) {
        popupGeometry.moveRight(screenGeometry.right());
    }
    if (popupGeometry.left() < screenGeometry.left()) {
        popupGeometry.moveLeft(screenGeometry.left());
    }

    // 调整Y坐标
    if (popupGeometry.bottom() > screenGeometry.bottom()) {
        popupGeometry.moveBottom(screenGeometry.bottom());
    }
    if (popupGeometry.top() < screenGeometry.top()) {
        popupGeometry.moveTop(screenGeometry.top());
    }

    setGeometry(popupGeometry);
}

void ToolboxPopup::adjustPositionToParent()
{
    if (!parentWidget()) return;

    QRect parentGeometry = parentWidget()->rect();
    QRect popupGeometry = geometry();

    // 调整X坐标
    if (popupGeometry.right() > parentGeometry.right()) {
        popupGeometry.moveRight(parentGeometry.right());
    }
    if (popupGeometry.left() < parentGeometry.left()) {
        popupGeometry.moveLeft(parentGeometry.left());
    }

    // 调整Y坐标
    if (popupGeometry.bottom() > parentGeometry.bottom()) {
        popupGeometry.moveBottom(parentGeometry.bottom());
    }
    if (popupGeometry.top() < parentGeometry.top()) {
        popupGeometry.moveTop(parentGeometry.top());
    }

    setGeometry(popupGeometry);
    qDebug() << "ToolboxPopup: 调整位置以适应父组件边界，新位置:" << popupGeometry;
}

void ToolboxPopup::removeGlobalEventFilter()
{
    if (m_globalFilterInstalled) {
        QApplication::instance()->removeEventFilter(this);
        m_globalFilterInstalled = false;
        qDebug() << "ToolboxPopup: 已移除全局事件过滤器";
    }
}
