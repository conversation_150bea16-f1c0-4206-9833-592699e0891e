.select-container[data-v-8df7386d] {
  width: 24.66667rem;
  height: 5.66667rem;
  justify-content: space-between;
  position: relative;
}
.select-text[data-v-8df7386d] {
  font-family: DOUYINSANSBOLD, sans-serif;
  font-size: 3.16667rem;
  color: #ffffff;
  line-height: 4rem;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.select-icon[data-v-8df7386d] {
  width: 3.16667rem;
  height: 3.16667rem;
  background: linear-gradient(209deg, #29da80 0%, #6053e3 100%);
  border-radius: 1.91667rem;
  color: #ffffff;
  text-align: center;
  line-height: 3.16667rem;
  margin: 0 1.58333rem;
  flex-shrink: 0;
}
.option-list[data-v-8df7386d] {
  width: 24.66667rem;
  max-height: 46.5rem;
  background: rgba(255, 255, 255);
  box-shadow: 0 0 0.75rem 0 rgba(14, 0, 151, 0.1);
  border-radius: 3.16667rem;
  position: relative;
  left: 1.58333rem;
  padding: 2.33333rem 1.58333rem;
  box-sizing: border-box;
}
.option-item[data-v-8df7386d] {
  font-size: 1.75rem;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.75rem;
  margin-bottom: 1.58333rem;
  justify-content: space-between;
}
.option-item[data-v-8df7386d]:last-child {
  margin-bottom: 0;
}
.option-item.is-selected[data-v-8df7386d] {
  font-weight: 600;
  color: #5f52e3;
}
.plan-popover {
  padding: 0 !important;
  width: auto !important;
  border-radius: 3.16667rem !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none;
}
.plan-popover.el-popover.el-popper {
  box-shadow: none;
  transition: none !important;
}
.fade-in-linear-enter-active,.fade-in-linear-leave-active{transition:var(--el-transition-fade-linear)}.fade-in-linear-enter-from,.fade-in-linear-leave-to{opacity:0}.el-fade-in-linear-enter-active,.el-fade-in-linear-leave-active{transition:var(--el-transition-fade-linear)}.el-fade-in-linear-enter-from,.el-fade-in-linear-leave-to{opacity:0}.el-fade-in-enter-active,.el-fade-in-leave-active{transition:all var(--el-transition-duration) cubic-bezier(.55,0,.1,1)}.el-fade-in-enter-from,.el-fade-in-leave-active{opacity:0}.el-zoom-in-center-enter-active,.el-zoom-in-center-leave-active{transition:all var(--el-transition-duration) cubic-bezier(.55,0,.1,1)}.el-zoom-in-center-enter-from,.el-zoom-in-center-leave-active{opacity:0;transform:scaleX(0)}.el-zoom-in-top-enter-active,.el-zoom-in-top-leave-active{opacity:1;transform:scaleY(1);transform-origin:center top;transition:var(--el-transition-md-fade)}.el-zoom-in-top-enter-active[data-popper-placement^=top],.el-zoom-in-top-leave-active[data-popper-placement^=top]{transform-origin:center bottom}.el-zoom-in-top-enter-from,.el-zoom-in-top-leave-active{opacity:0;transform:scaleY(0)}.el-zoom-in-bottom-enter-active,.el-zoom-in-bottom-leave-active{opacity:1;transform:scaleY(1);transform-origin:center bottom;transition:var(--el-transition-md-fade)}.el-zoom-in-bottom-enter-from,.el-zoom-in-bottom-leave-active{opacity:0;transform:scaleY(0)}.el-zoom-in-left-enter-active,.el-zoom-in-left-leave-active{opacity:1;transform:scale(1);transform-origin:top left;transition:var(--el-transition-md-fade)}.el-zoom-in-left-enter-from,.el-zoom-in-left-leave-active{opacity:0;transform:scale(.45)}.collapse-transition{transition:var(--el-transition-duration) height ease-in-out,var(--el-transition-duration) padding-top ease-in-out,var(--el-transition-duration) padding-bottom ease-in-out}.el-collapse-transition-enter-active,.el-collapse-transition-leave-active{transition:var(--el-transition-duration) max-height ease-in-out,var(--el-transition-duration) padding-top ease-in-out,var(--el-transition-duration) padding-bottom ease-in-out}.horizontal-collapse-transition{transition:var(--el-transition-duration) width ease-in-out,var(--el-transition-duration) padding-left ease-in-out,var(--el-transition-duration) padding-right ease-in-out}.el-list-enter-active,.el-list-leave-active{transition:all 1s}.el-list-enter-from,.el-list-leave-to{opacity:0;transform:translateY(-2.5rem)}.el-list-leave-active{position:absolute!important}.el-opacity-transition{transition:opacity var(--el-transition-duration) cubic-bezier(.55,0,.1,1)}.el-tree{--el-tree-node-content-height:2.16667rem;--el-tree-node-hover-bg-color:var(--el-fill-color-light);--el-tree-text-color:var(--el-text-color-regular);--el-tree-expand-icon-color:var(--el-text-color-placeholder);background:var(--el-fill-color-blank);color:var(--el-tree-text-color);cursor:default;font-size:var(--el-font-size-base);position:relative}.el-tree__empty-block{height:100%;min-height:5rem;position:relative;text-align:center;width:100%}.el-tree__empty-text{color:var(--el-text-color-secondary);font-size:var(--el-font-size-base);left:50%;position:absolute;top:50%;transform:translate(-50%,-50%)}.el-tree__drop-indicator{background-color:var(--el-color-primary);height:0.08333rem;left:0;position:absolute;right:0}.el-tree-node{outline:none;white-space:nowrap}.el-tree-node:focus>.el-tree-node__content{background-color:var(--el-tree-node-hover-bg-color)}.el-tree-node.is-drop-inner>.el-tree-node__content .el-tree-node__label{background-color:var(--el-color-primary);color:#fff}.el-tree-node__content{--el-checkbox-height:var(--el-tree-node-content-height);align-items:center;cursor:pointer;display:flex;height:var(--el-tree-node-content-height)}.el-tree-node__content>.el-tree-node__expand-icon{box-sizing:content-box;padding:0.5rem}.el-tree-node__content>label.el-checkbox{margin-right:0.66667rem}.el-tree-node__content:hover{background-color:var(--el-tree-node-hover-bg-color)}.el-tree.is-dragging .el-tree-node__content{cursor:move}.el-tree.is-dragging .el-tree-node__content *{pointer-events:none}.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content{cursor:not-allowed}.el-tree-node__expand-icon{color:var(--el-tree-expand-icon-color);cursor:pointer;font-size:1rem;transform:rotate(0deg);transition:transform var(--el-transition-duration) ease-in-out}.el-tree-node__expand-icon.expanded{transform:rotate(90deg)}.el-tree-node__expand-icon.is-leaf{color:transparent;cursor:default;visibility:hidden}.el-tree-node__expand-icon.is-hidden{visibility:hidden}.el-tree-node__loading-icon{color:var(--el-tree-expand-icon-color);font-size:var(--el-font-size-base);margin-right:0.66667rem}.el-tree-node>.el-tree-node__children{background-color:transparent;overflow:hidden}.el-tree-node.is-expanded>.el-tree-node__children{display:block}.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{background-color:var(--el-color-primary-light-9)}.el-checkbox{--el-checkbox-font-size:1.16667rem;--el-checkbox-font-weight:var(--el-font-weight-primary);--el-checkbox-text-color:var(--el-text-color-regular);--el-checkbox-input-height:1.16667rem;--el-checkbox-input-width:1.16667rem;--el-checkbox-border-radius:var(--el-border-radius-small);--el-checkbox-bg-color:var(--el-fill-color-blank);--el-checkbox-input-border:var(--el-border);--el-checkbox-disabled-border-color:var(--el-border-color);--el-checkbox-disabled-input-fill:var(--el-fill-color-light);--el-checkbox-disabled-icon-color:var(--el-text-color-placeholder);--el-checkbox-disabled-checked-input-fill:var(--el-border-color-extra-light);--el-checkbox-disabled-checked-input-border-color:var(--el-border-color);--el-checkbox-disabled-checked-icon-color:var(--el-text-color-placeholder);--el-checkbox-checked-text-color:var(--el-color-primary);--el-checkbox-checked-input-border-color:var(--el-color-primary);--el-checkbox-checked-bg-color:var(--el-color-primary);--el-checkbox-checked-icon-color:var(--el-color-white);--el-checkbox-input-border-color-hover:var(--el-color-primary);align-items:center;color:var(--el-checkbox-text-color);cursor:pointer;display:inline-flex;font-size:var(--el-font-size-base);font-weight:var(--el-checkbox-font-weight);height:var(--el-checkbox-height,2.66667rem);margin-right:2.5rem;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap}.el-checkbox.is-disabled{cursor:not-allowed}.el-checkbox.is-bordered{border:var(--el-border);border-radius:var(--el-border-radius-base);box-sizing:border-box;padding:0 1.25rem 0 0.75rem}.el-checkbox.is-bordered.is-checked{border-color:var(--el-color-primary)}.el-checkbox.is-bordered.is-disabled{border-color:var(--el-border-color-lighter)}.el-checkbox.is-bordered.el-checkbox--large{border-radius:var(--el-border-radius-base);padding:0 1.58333rem 0 0.91667rem}.el-checkbox.is-bordered.el-checkbox--large .el-checkbox__label{font-size:var(--el-font-size-base)}.el-checkbox.is-bordered.el-checkbox--large .el-checkbox__inner{height:1.16667rem;width:1.16667rem}.el-checkbox.is-bordered.el-checkbox--small{border-radius:calc(var(--el-border-radius-base) - 0.08333rem);padding:0 0.91667rem 0 0.58333rem}.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__label{font-size:1rem}.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__inner{height:1rem;width:1rem}.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__inner:after{height:0.5rem;width:0.16667rem}.el-checkbox input:focus-visible+.el-checkbox__inner{border-radius:var(--el-checkbox-border-radius);outline:0.16667rem solid var(--el-checkbox-input-border-color-hover);outline-offset:0.08333rem}.el-checkbox__input{cursor:pointer;display:inline-flex;outline:none;position:relative;white-space:nowrap}.el-checkbox__input.is-disabled .el-checkbox__inner{background-color:var(--el-checkbox-disabled-input-fill);border-color:var(--el-checkbox-disabled-border-color);cursor:not-allowed}.el-checkbox__input.is-disabled .el-checkbox__inner:after{border-color:var(--el-checkbox-disabled-icon-color);cursor:not-allowed}.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner{background-color:var(--el-checkbox-disabled-checked-input-fill);border-color:var(--el-checkbox-disabled-checked-input-border-color)}.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner:after{border-color:var(--el-checkbox-disabled-checked-icon-color)}.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner{background-color:var(--el-checkbox-disabled-checked-input-fill);border-color:var(--el-checkbox-disabled-checked-input-border-color)}.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner:before{background-color:var(--el-checkbox-disabled-checked-icon-color);border-color:var(--el-checkbox-disabled-checked-icon-color)}.el-checkbox__input.is-disabled+span.el-checkbox__label{color:var(--el-disabled-text-color);cursor:not-allowed}.el-checkbox__input.is-checked .el-checkbox__inner{background-color:var(--el-checkbox-checked-bg-color);border-color:var(--el-checkbox-checked-input-border-color)}.el-checkbox__input.is-checked .el-checkbox__inner:after{border-color:var(--el-checkbox-checked-icon-color);transform:rotate(45deg) scaleY(1)}.el-checkbox__input.is-checked+.el-checkbox__label{color:var(--el-checkbox-checked-text-color)}.el-checkbox__input.is-focus:not(.is-checked) .el-checkbox__original:not(:focus-visible){border-color:var(--el-checkbox-input-border-color-hover)}.el-checkbox__input.is-indeterminate .el-checkbox__inner{background-color:var(--el-checkbox-checked-bg-color);border-color:var(--el-checkbox-checked-input-border-color)}.el-checkbox__input.is-indeterminate .el-checkbox__inner:before{background-color:var(--el-checkbox-checked-icon-color);content:"";display:block;height:0.16667rem;left:0;position:absolute;right:0;top:0.41667rem;transform:scale(.5)}.el-checkbox__input.is-indeterminate .el-checkbox__inner:after{display:none}.el-checkbox__inner{background-color:var(--el-checkbox-bg-color);border:var(--el-checkbox-input-border);border-radius:var(--el-checkbox-border-radius);box-sizing:border-box;display:inline-block;height:var(--el-checkbox-input-height);position:relative;transition:border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46),outline .25s cubic-bezier(.71,-.46,.29,1.46);width:var(--el-checkbox-input-width);z-index:var(--el-index-normal)}.el-checkbox__inner:hover{border-color:var(--el-checkbox-input-border-color-hover)}.el-checkbox__inner:after{border:0.08333rem solid transparent;border-left:0;border-top:0;box-sizing:content-box;content:"";height:0.58333rem;left:0.33333rem;position:absolute;top:0.08333rem;transform:rotate(45deg) scaleY(0);transform-origin:center;transition:transform .15s ease-in .05s;width:0.25rem}.el-checkbox__original{height:0;margin:0;opacity:0;outline:none;position:absolute;width:0;z-index:-1}.el-checkbox__label{display:inline-block;font-size:var(--el-checkbox-font-size);line-height:1;padding-left:0.66667rem}.el-checkbox.el-checkbox--large{height:3.33333rem}.el-checkbox.el-checkbox--large .el-checkbox__label{font-size:1.16667rem}.el-checkbox.el-checkbox--large .el-checkbox__inner{height:1.16667rem;width:1.16667rem}.el-checkbox.el-checkbox--small{height:2rem}.el-checkbox.el-checkbox--small .el-checkbox__label{font-size:1rem}.el-checkbox.el-checkbox--small .el-checkbox__inner{height:1rem;width:1rem}.el-checkbox.el-checkbox--small .el-checkbox__input.is-indeterminate .el-checkbox__inner:before{top:0.33333rem}.el-checkbox.el-checkbox--small .el-checkbox__inner:after{height:0.5rem;width:0.16667rem}.el-checkbox:last-of-type{margin-right:0}.resource-tree[data-v-22a17eb3] {
  width: 26.91667rem;
  height: 46.5rem;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.45) 100%);
  border-radius: 3.16667rem 0 0 3.16667rem;
  opacity: 0.9;
  padding: 1.58333rem 2.41667rem 1.58333rem 1.58333rem;
  box-sizing: border-box;
  overflow: auto;
}
.resource-tree[data-v-22a17eb3] .el-tree__empty-text {
  font-weight: 400;
  font-size: 1.91667rem;
  color: rgba(95, 82, 227, 0.65);
}
.custom-tree-node[data-v-22a17eb3] {
  width: 100%;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  align-items: center;
  padding-right: 0.66667rem;
  padding-left: 3.16667rem;
  position: relative;
}
.custom-tree-node .node-label[data-v-22a17eb3] {
  flex-grow: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: break-spaces;
}
.custom-tree-node .custom-arrow[data-v-22a17eb3] {
  position: absolute;
  left: 0;
  color: rgba(0, 0, 0, 0.25);
}
.custom-tree-node .custom-arrow.is-expanded[data-v-22a17eb3] {
  transform: rotate(90deg);
}
.custom-tree-node .leaf-icon[data-v-22a17eb3] {
  position: absolute;
  left: 0;
  color: #29da80;
  font-size: 1.91667rem;
}
.custom-tree-node[data-v-22a17eb3]::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: -1.58333rem;
  left: -2.16667rem;
  width: 0;
  border-left: 0.08333rem dashed rgba(95, 82, 227, 0.5);
}
.level-1[data-v-22a17eb3] {
  font-weight: 600;
  font-size: 2.16667rem;
  line-height: 2.66667rem;
  padding-bottom: 1.75rem;
  color: rgba(0, 0, 0, 0.85);
}
.level-2[data-v-22a17eb3] {
  font-size: 1.91667rem;
  line-height: 2.16667rem;
  padding-bottom: 1.58333rem;
  padding-left: 3.16667rem;
  color: rgba(0, 0, 0, 0.85);
}
.level-3[data-v-22a17eb3] {
  font-size: 1.91667rem;
  line-height: 2.16667rem;
  padding-bottom: 1.16667rem;
  padding-left: 6.33333rem;
  color: rgba(0, 0, 0, 0.68);
  position: relative;
}
.level-3 .custom-arrow[data-v-22a17eb3] {
  left: 3.16667rem;
}
.level-3[data-v-22a17eb3]::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: -1.58333rem;
  left: 1rem;
  width: 0;
  border-left: 0.08333rem dashed rgba(95, 82, 227, 0.5);
}
.is-highlight[data-v-22a17eb3] {
  color: #5f52e3;
}
.is-highlight .custom-arrow[data-v-22a17eb3] {
  color: #5f52e3;
}
.is-highlight.level-3 .custom-tree-node[data-v-22a17eb3] {
  color: rgba(95, 82, 227, 0.65);
}
.is-selected.level-2[data-v-22a17eb3] {
  font-weight: 600;
}
[data-v-22a17eb3] .el-tree {
  background: transparent;
}
[data-v-22a17eb3] .el-tree .el-tree-node__children {
  transition: none !important;
}
[data-v-22a17eb3] .el-tree .el-tree-node__content {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  height: auto;
}
[data-v-22a17eb3] .el-tree .el-tree-node__content:hover {
  background-color: transparent;
}
[data-v-22a17eb3] .el-tree .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent;
}
[data-v-22a17eb3] .el-tree .el-tree-node__expand-icon {
  transition: none !important;
  display: none;
}
.el-popconfirm__main{align-items:center;display:flex}.el-popconfirm__icon{margin-right:0.41667rem}.el-popconfirm__action{margin-top:0.66667rem;text-align:right}.progress-bar-container[data-v-89bce9df] {
  height: 1.91667rem;
  position: relative;
}
.progress-bar[data-v-89bce9df] {
  flex: 1;
  height: 0.66667rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0.33333rem;
  position: relative;
  overflow: hidden;
}
.progress-bar-fill[data-v-89bce9df] {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #29da80;
  border-radius: 0.33333rem;
}
.progress-icon[data-v-89bce9df] {
  position: absolute;
  font-size: 1.5rem;
  color: #29da80;
  transform: translateX(-1.33333rem);
}
.progress-icon.finish[data-v-89bce9df] {
  width: 1.33333rem;
  height: 1.33333rem;
  border-radius: 1.33333rem;
  border: 0.16667rem solid #ffffff;
  background: #29da80;
  color: #ffffff;
  line-height: 1.16667rem;
  font-size: 0.83333rem;
  text-align: center;
}
.resource-image[data-v-7a88dfd0] {
  width: 19.5rem;
  height: 10.91667rem;
  border-radius: 1.58333rem 1.58333rem 0 0;
  overflow: hidden;
}
.resource-image .image[data-v-7a88dfd0] {
  width: 100%;
  height: 100%;
}
.resource-info[data-v-7a88dfd0] {
  width: 19.5rem;
  background: rgba(95, 82, 227, 0.1);
  border-radius: 0 0 1.58333rem 1.58333rem;
  padding: 1.33333rem 1.58333rem 1.33333rem 1.58333rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.resource-info.material[data-v-7a88dfd0] {
  height: 4.08333rem;
}
.resource-title[data-v-7a88dfd0] {
  font-size: 1.75rem;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.75rem;
  min-height: 2rem;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.resource-title-wrap[data-v-7a88dfd0] {
  height: 3rem;
  margin-bottom: 0.5rem;
}
.resource-progress[data-v-7a88dfd0] {
  font-size: 1.33333rem;
  color: rgba(0, 0, 0, 0.45);
  line-height: 1.33333rem;
  margin-top: 0.25rem;
}
.transcoding[data-v-7a88dfd0] {
  width: 100%;
  height: 100%;
  background: linear-gradient(138deg, #9b9fe6 0%, #6053e3 100%);
}
.transcoding .text[data-v-7a88dfd0] {
  font-weight: 600;
  font-size: 1.75rem;
  color: #ffffff;
  margin-top: 0.75rem;
}
.error[data-v-7a88dfd0] {
  color: #5f52e3;
  font-size: 3.33333rem;
  width: 100%;
  height: 100%;
  background: linear-gradient(138deg, #efefef 0%, #cecece 100%);
}
.error .text[data-v-7a88dfd0] {
  font-weight: 600;
  font-size: 1.75rem;
  margin-top: 0.75rem;
}
.resource-image[data-v-fdf11806] {
  height: 7rem;
  border-radius: 1.58333rem 1.58333rem 0 0;
  overflow: hidden;
}
.resource-image .image[data-v-fdf11806] {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* 填充容器，可能裁剪图片 */
  object-position: top center;
  /* 将图片的顶部居中 */
}
.resource-info[data-v-fdf11806] {
  width: 11.08333rem;
  background: rgba(95, 82, 227, 0.1);
  border-radius: 0 0 1.58333rem 1.58333rem;
  padding: 0.66667rem 0.79167rem 0.66667rem 0.79167rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.resource-title[data-v-fdf11806] {
  font-size: 1.75rem;
  color: rgba(0, 0, 0, 0.85);
  line-height: 2.33333rem;
  word-break: break-all;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.uploading-wrapper[data-v-63c5c183] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(1rem);
}
.uploading-container[data-v-63c5c183] {
  width: 68rem;
  position: relative;
}
.bg-top[data-v-63c5c183] {
  height: 7.16667rem;
  width: 100%;
}
.bg-top img[data-v-63c5c183] {
  width: 100%;
  height: 100%;
}
.uploading-content[data-v-63c5c183] {
  width: 100%;
  background: #ffffff;
  border-radius: 3.16667rem;
  box-sizing: border-box;
  padding: 0 4.25rem 3.16667rem 4.25rem;
  position: relative;
  top: -2.83333rem;
}
.uploading-content .title[data-v-63c5c183] {
  font-family: var(--font-family-title);
  font-size: 3.16667rem;
  color: #5f52e3;
  line-height: 3.75rem;
  letter-spacing: 0.16667rem;
  margin-bottom: 1.41667rem;
}
.uploading-image-box[data-v-63c5c183] {
  width: 26.91667rem;
  height: 26.91667rem;
  z-index: 2;
  position: relative;
  bottom: -5.33333rem;
  font-family: var(--font-family-number);
  font-weight: bold;
  font-size: 5.41667rem;
  color: #ffffff;
}
.uploading-image-box span[data-v-63c5c183] {
  z-index: 2;
}
.uploading-image-box .image[data-v-63c5c183] {
  width: 100%;
  height: 100%;
  position: absolute;
}
.uploading-progress-bar[data-v-63c5c183] {
  width: 31.25rem;
  height: 2.5rem;
  position: relative;
}
.progress-bar[data-v-63c5c183] {
  flex: 1;
  height: 1.58333rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0.83333rem;
  position: relative;
  overflow: hidden;
}
.progress-bar-fill[data-v-63c5c183] {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #29da80;
  border-radius: 0.83333rem;
}
.progress-icon[data-v-63c5c183] {
  position: absolute;
  font-size: 2.5rem;
  line-height: 2.5rem;
  color: #07b95e;
  transform: translateX(-0.83333rem) translateY(-0.08333rem);
}
.transcoding-tips[data-v-63c5c183] {
  margin-top: 1.25rem;
  font-size: 1.5rem;
  color: rgba(0, 0, 0, 0.25);
  line-height: 1.5rem;
  text-align: center;
}
.tips-container[data-v-63c5c183] {
  font-size: 2.16667rem;
  color: rgba(0, 0, 0, 0.65);
  line-height: 2.16667rem;
  margin-top: 5.08333rem;
}
.tips-container .title[data-v-63c5c183] {
  font-family: var(--font-family);
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}
.tips-container .text[data-v-63c5c183]:not(:last-child) {
  margin-bottom: 1.75rem;
}
.tips-container .highlight[data-v-63c5c183] {
  display: inline-block;
  margin: 0 1.5rem;
  color: #5f52e3;
  font-weight: 600;
  font-size: 2.16667rem;
  line-height: 3.41667rem;
  height: 3.41667rem;
  background: linear-gradient(90deg, rgba(95, 82, 227, 0) 0%, rgba(95, 82, 227, 0.2) 49%, rgba(95, 82, 227, 0) 100%);
}
.minimize[data-v-63c5c183] {
  color: #ffffff;
  font-weight: 600;
  font-size: 2.16667rem;
  line-height: 2.16667rem;
}
.minimize-icon[data-v-63c5c183] {
  font-size: 1.83333rem;
  line-height: 2.16667rem;
}
/* 定义旋转动画 */
@keyframes rotate-63c5c183 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.resource-container[data-v-ac0b02ba] {
  width: 100%;
  height: 58.58333rem;
  background: #ffffff;
  border-radius: 3.16667rem;
  backdrop-filter: blur(1.83333rem);
  padding: 2.91667rem;
  overflow: hidden;
  box-sizing: border-box;
}
.title-text[data-v-ac0b02ba] {
  font-weight: 600;
  font-size: 1.91667rem;
  color: rgba(0, 0, 0, 0.45);
}
.title-text[data-v-ac0b02ba]:not(:first-child) {
  margin-top: 2.33333rem;
}
.scroll-container[data-v-ac0b02ba] {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
}
.scroll-container[data-v-ac0b02ba]::-webkit-scrollbar {
  width: 0.5rem;
}
.scroll-container[data-v-ac0b02ba]::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
}
.resource-list[data-v-ac0b02ba] {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.58333rem;
  margin-top: 1.16667rem;
  min-height: 20.08333rem;
}
.resource-list.material[data-v-ac0b02ba] {
  grid-template-columns: repeat(5, 1fr);
  min-height: 13.33333rem;
}
.bottom-button[data-v-ac0b02ba] {
  gap: 7.83333rem;
  font-weight: 600;
  font-size: 1.91667rem;
  color: #5f52e3;
  line-height: 1.91667rem;
  padding-top: 0.66667rem;
}
.mobile-qrcode-container[data-v-ac0b02ba] {
  width: 14.16667rem;
  font-size: 1.41667rem;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.91667rem;
}
.mobile-qrcode-container .qrcode[data-v-ac0b02ba] {
  width: 10.66667rem;
  height: 10.66667rem;
}
.import-button[data-v-ac0b02ba] {
  gap: 0.75rem;
}
.no-data[data-v-ac0b02ba] {
  font-size: 1.91667rem;
  color: rgba(0, 0, 0, 0.25);
}
.confirm-button-group[data-v-ac0b02ba] {
  text-align: center;
  margin-top: 1.58333rem;
}
.qrcode-popover.el-popover.el-popper {
  width: auto !important;
  border-radius: 1.25rem;
  padding: 1rem 1.58333rem 1.41667rem 1.58333rem;
  box-sizing: border-box;
}
.qrcode-popover .setting-wrapper {
  gap: 1.58333rem;
}
[data-v-07dbed59] .dialog-title {
  padding: 0;
}
.resource-dialog-container[data-v-07dbed59] {
  width: 95.75rem;
  height: 58.58333rem;
  position: relative;
  border-radius: 3.16667rem;
  overflow: hidden;
}
.resource-tree-container[data-v-07dbed59] {
  width: 27.83333rem;
  padding: 2.33333rem 0 3.5rem 3.16667rem;
  box-sizing: border-box;
  z-index: 1;
}
.resource-list-container[data-v-07dbed59] {
  z-index: 2;
  flex-grow: 1;
}
.background[data-v-07dbed59] {
  width: 28.58333rem;
  height: 19rem;
  background: #29da80;
  filter: blur(5.83333rem);
  position: absolute;
  left: -8.33333rem;
  top: -6.66667rem;
  border-radius: 3.16667rem;
  z-index: 0;
}
