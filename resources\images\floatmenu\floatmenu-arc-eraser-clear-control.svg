<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 40</title>
    <defs>
        <circle id="path-1" cx="24" cy="24" r="24"></circle>
        <filter x="-17.7%" y="-17.7%" width="135.4%" height="135.4%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="8" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.243686692   0 0 0 0 0.242040323   0 0 0 0 0.728091033  0 0 0 0.818673514 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="V1.2.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="白板备份" transform="translate(-516, -1099)">
            <g id="编组-40" transform="translate(516, 1099)">
                <g id="椭圆形">
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                </g>
                <path d="M13.2576283,37.3584857 C13.2576283,37.3584857 23.4621746,45.766389 37.0636587,37.3584857" id="路径-8" stroke="#FFFFFF" stroke-width="5" stroke-linecap="round"></path>
            </g>
        </g>
    </g>
</svg>