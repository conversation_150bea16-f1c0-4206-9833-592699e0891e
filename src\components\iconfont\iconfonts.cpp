#include "iconfonts.h"
#include "iconfontmanager.h"
#include <QDebug>

namespace IconFonts {

    // 静态变量，标记是否已初始化
    static bool s_initialized = false;

    QIcon createIcon(int unicode, const QSize& size, const QColor& color)
    {
        if (!s_initialized) {
            qWarning() << "IconFonts not initialized. Call IconFonts::initialize() first.";
            return QIcon();
        }
        return IconFontManager::instance()->createIcon(unicode, size, color);
    }

    QIcon createToolbarIcon(int unicode, const QColor& color)
    {
        return createIcon(unicode, Defaults::TOOLBAR_SIZE, color);
    }

    QIcon createSmallIcon(int unicode, const QColor& color)
    {
        return createIcon(unicode, Defaults::SMALL_SIZE, color);
    }

    QIcon createLargeIcon(int unicode, const QColor& color)
    {
        return createIcon(unicode, Defaults::LARGE_SIZE, color);
    }

    QIcon createMultiStateIcon(int unicode, const QSize& size)
    {
        if (!s_initialized) {
            qWarning() << "IconFonts not initialized. Call IconFonts::initialize() first.";
            return QIcon();
        }

        QIcon icon;
        IconFontManager* manager = IconFontManager::instance();

        // 正常状态
        QPixmap normalPixmap = manager->createPixmap(unicode, size, Defaults::DEFAULT_COLOR);
        icon.addPixmap(normalPixmap, QIcon::Normal, QIcon::Off);

        // 悬停状态
        QPixmap hoverPixmap = manager->createPixmap(unicode, size, Defaults::HOVER_COLOR);
        icon.addPixmap(hoverPixmap, QIcon::Active, QIcon::Off);

        // 激活状态
        QPixmap activePixmap = manager->createPixmap(unicode, size, Defaults::ACTIVE_COLOR);
        icon.addPixmap(activePixmap, QIcon::Selected, QIcon::On);

        // 禁用状态
        QPixmap disabledPixmap = manager->createPixmap(unicode, size, Defaults::DISABLED_COLOR);
        icon.addPixmap(disabledPixmap, QIcon::Disabled, QIcon::Off);

        return icon;
    }

    bool initialize(const QString& fontPath, const QString& fontFamily)
    {
        IconFontManager* manager = IconFontManager::instance();
        bool success;

        if (fontPath.isEmpty()) {
            // 如果没有指定路径，使用默认的资源字体
            success = manager->loadDefaultIconFont();
        } else {
            success = manager->loadFont(fontPath, fontFamily);
        }

        if (success) {
            s_initialized = true;
            QString loadedFont = fontPath.isEmpty() ? "default resource font" : fontPath;
            qDebug() << "IconFonts initialized successfully with font:" << loadedFont;
        } else {
            QString attemptedFont = fontPath.isEmpty() ? "default resource font" : fontPath;
            qWarning() << "Failed to initialize IconFonts with font:" << attemptedFont;
        }

        return success;
    }

    bool isInitialized()
    {
        return s_initialized;
    }

    // ==================== 预定义图标创建方法实现 ====================

    // 基础操作图标
    QIcon saveIcon(const QSize& size) { return createIcon(Icons::SAVE, size); }
    QIcon exitIcon(const QSize& size) { return createIcon(Icons::EXIT, size); }
    QIcon refreshIcon(const QSize& size) { return createIcon(Icons::REFRESH, size); }
    QIcon settingIcon(const QSize& size) { return createIcon(Icons::SETTING, size); }
    QIcon shutdownIcon(const QSize& size) { return createIcon(Icons::SHUTDOWN, size); }
    QIcon closeIcon(const QSize& size) { return createIcon(Icons::CLOSE, size); }
    QIcon workspaceSettingIcon(const QSize& size) { return createIcon(Icons::WORKSPACE_SETTING, size); }
    QIcon layoutSettingIcon(const QSize& size) { return createIcon(Icons::LAYOUT_SETTING, size); }
    QIcon addTimerIcon(const QSize& size) { return createIcon(Icons::ADD_TIMER, size); }

    // 编辑操作图标
    QIcon undoIcon(const QSize& size) { return createIcon(Icons::UNDO, size); }
    QIcon deleteIcon(const QSize& size) { return createIcon(Icons::DELETE, size); }
    QIcon deletePlainIcon(const QSize& size) { return createIcon(Icons::DELETE_PLAIN, size); }
    QIcon deleteDialogIcon(const QSize& size) { return createIcon(Icons::DELETE_DIALOG, size); }
    QIcon clearIcon(const QSize& size) { return createIcon(Icons::CLEAR, size); }
    QIcon selectIcon(const QSize& size) { return createIcon(Icons::SELECT, size); }
    QIcon plusIcon(const QSize& size) { return createIcon(Icons::PLUS, size); }
    QIcon minusIcon(const QSize& size) { return createIcon(Icons::MINUS, size); }

    // 绘图工具图标
    QIcon selectToolIcon(const QSize& size) { return createIcon(Icons::SELECT_TOOL, size); }
    QIcon penToolIcon(const QSize& size) { return createIcon(Icons::PEN_TOOL, size); }
    QIcon penNormalIcon(const QSize& size) { return createIcon(Icons::PEN_NORMAL, size); }
    QIcon penMarkerIcon(const QSize& size) { return createIcon(Icons::PEN_MARKER, size); }
    QIcon penWatercolorIcon(const QSize& size) { return createIcon(Icons::PEN_WATERCOLOR, size); }
    QIcon penDashedIcon(const QSize& size) { return createIcon(Icons::PEN_DASHED, size); }
    QIcon lineToolIcon(const QSize& size) { return createIcon(Icons::LINE_TOOL, size); }
    QIcon dashedLineIcon(const QSize& size) { return createIcon(Icons::DASHED_LINE, size); }
    QIcon eraserIcon(const QSize& size) { return createIcon(Icons::ERASER, size); }

    // 笔刷相关图标
    QIcon watercolorBrushIcon(const QSize& size) { return createIcon(Icons::WATERCOLOR_BRUSH, size); }
    QIcon markerBrushIcon(const QSize& size) { return createIcon(Icons::MARKER_BRUSH, size); }
    QIcon dashedBrushIcon(const QSize& size) { return createIcon(Icons::DASHED_BRUSH, size); }
    QIcon markerTipIcon(const QSize& size) { return createIcon(Icons::MARKER_TIP, size); }
    QIcon watercolorTipIcon(const QSize& size) { return createIcon(Icons::WATERCOLOR_TIP, size); }
    QIcon dashedColorIcon(const QSize& size) { return createIcon(Icons::DASHED_COLOR, size); }
    QIcon markerBrushFullIcon(const QSize& size) { return createIcon(Icons::MARKER_BRUSH_FULL, size); }
    QIcon watercolorBrushFullIcon(const QSize& size) { return createIcon(Icons::WATERCOLOR_BRUSH_FULL, size); }
    QIcon dashedPenIcon(const QSize& size) { return createIcon(Icons::DASHED_PEN, size); }

    // 几何图形图标
    QIcon circleIcon(const QSize& size) { return createIcon(Icons::CIRCLE, size); }
    QIcon ellipseIcon(const QSize& size) { return createIcon(Icons::ELLIPSE, size); }
    QIcon rectangleIcon(const QSize& size) { return createIcon(Icons::RECTANGLE, size); }
    QIcon squareIcon(const QSize& size) { return createIcon(Icons::SQUARE, size); }
    QIcon triangleIcon(const QSize& size) { return createIcon(Icons::TRIANGLE, size); }
    QIcon rightTriangleIcon(const QSize& size) { return createIcon(Icons::RIGHT_TRIANGLE, size); }
    QIcon arrowIcon(const QSize& size) { return createIcon(Icons::ARROW, size); }
    QIcon graphicIcon(const QSize& size) { return createIcon(Icons::GRAPHIC, size); }

    // 界面控制图标
    QIcon arrowLeftIcon(const QSize& size) { return createIcon(Icons::ARROW_LEFT, size); }
    QIcon arrowRightIcon(const QSize& size) { return createIcon(Icons::ARROW_RIGHT, size); }
    QIcon arrowDownIcon(const QSize& size) { return createIcon(Icons::ARROW_DOWN, size); }
    QIcon fullscreenIcon(const QSize& size) { return createIcon(Icons::FULLSCREEN, size); }
    QIcon minimizeIcon(const QSize& size) { return createIcon(Icons::MINIMIZE, size); }
    QIcon rotateIcon(const QSize& size) { return createIcon(Icons::ROTATE, size); }

    // 工具和功能图标
    QIcon toolboxIcon(const QSize& size) { return createIcon(Icons::TOOLBOX, size); }
    QIcon toolboxSelectIcon(const QSize& size) { return createIcon(Icons::TOOLBOX_SELECT, size); }
    QIcon resourceIcon(const QSize& size) { return createIcon(Icons::RESOURCE, size); }
    QIcon resourceSelectIcon(const QSize& size) { return createIcon(Icons::RESOURCE_SELECT, size); }
    QIcon magnifierIcon(const QSize& size) { return createIcon(Icons::MAGNIFIER, size); }
    QIcon timerIcon(const QSize& size) { return createIcon(Icons::TIMER, size); }
    QIcon rollNameIcon(const QSize& size) { return createIcon(Icons::ROLL_NAME, size); }
    QIcon teacherIcon(const QSize& size) { return createIcon(Icons::TEACHER, size); }
    QIcon phoneIcon(const QSize& size) { return createIcon(Icons::PHONE, size); }
    QIcon lockIcon(const QSize& size) { return createIcon(Icons::LOCK, size); }

    // 教学相关图标
    QIcon coursewareIcon(const QSize& size) { return createIcon(Icons::COURSEWARE, size); }
    QIcon materialIcon(const QSize& size) { return createIcon(Icons::MATERIAL, size); }
    QIcon thumbnailIcon(const QSize& size) { return createIcon(Icons::THUMBNAIL, size); }

    // 灯光控制图标
    QIcon lightOnIcon(const QSize& size) { return createIcon(Icons::LIGHT_ON, size); }
    QIcon lightOffIcon(const QSize& size) { return createIcon(Icons::LIGHT_OFF, size); }

    // 网络和应用图标
    QIcon networkIcon(const QSize& size) { return createIcon(Icons::NETWORK, size); }
    QIcon noNetworkIcon(const QSize& size) { return createIcon(Icons::NO_NETWORK, size); }
    QIcon moreAppIcon(const QSize& size) { return createIcon(Icons::MORE_APP, size); }
    QIcon wechatIcon(const QSize& size) { return createIcon(Icons::WECHAT, size); }
    QIcon larkIcon(const QSize& size) { return createIcon(Icons::LARK, size); }
    QIcon dingtalkIcon(const QSize& size) { return createIcon(Icons::DINGTALK, size); }

    // 上传功能图标
    QIcon uploadVideoIcon(const QSize& size) { return createIcon(Icons::UPLOAD_VIDEO, size); }
    QIcon uploadImageIcon(const QSize& size) { return createIcon(Icons::UPLOAD_IMAGE, size); }

} // namespace IconFonts
