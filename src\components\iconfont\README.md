# IconFont 管理器使用指南

这是一个为Qt应用程序设计的IconFont管理系统，支持从iconfont字体文件生成高质量的图标。

## 功能特性

- ✅ 支持TTF、WOFF、WOFF2字体格式
- ✅ 智能缓存机制，提升性能
- ✅ 支持动态颜色和大小调整
- ✅ 多状态图标支持（正常、悬停、激活、禁用）
- ✅ 预定义常用图标，开箱即用
- ✅ 单例模式，全局访问
- ✅ 基于实际iconfont项目的Unicode映射

## 快速开始

### 1. 初始化系统

```cpp
#include "src/components/iconfont/iconfonts.h"

// 在应用程序启动时初始化（使用资源中的默认字体）
if (!IconFonts::initialize()) {
    qWarning() << "IconFont初始化失败";
}

// 或者指定自定义字体文件
// IconFonts::initialize("/path/to/your/font.ttf", "YourFontFamily");
```

### 2. 创建图标

#### 使用预定义的便捷方法
```cpp
// 创建工具栏图标
QAction *saveAction = new QAction(IconFonts::saveIcon(), "保存", this);
QAction *undoAction = new QAction(IconFonts::undoIcon(), "撤销", this);
QAction *penAction = new QAction(IconFonts::penToolIcon(), "画笔", this);

// 创建按钮图标
QPushButton *btn = new QPushButton("设置", this);
btn->setIcon(IconFonts::settingIcon());
```

#### 使用Unicode值直接创建
```cpp
// 使用预定义的Unicode常量
QIcon icon1 = IconFonts::createIcon(IconFonts::Icons::SAVE);
QIcon icon2 = IconFonts::createIcon(IconFonts::Icons::ERASER);

// 指定大小和颜色
QIcon icon3 = IconFonts::createIcon(IconFonts::Icons::CIRCLE, 
                                   QSize(32, 32), 
                                   QColor(Qt::red));
```

#### 创建多状态图标
```cpp
// 自动生成正常、悬停、激活、禁用状态
QIcon multiStateIcon = IconFonts::createMultiStateIcon(IconFonts::Icons::TOOLBOX);
QPushButton *btn = new QPushButton("工具箱", this);
btn->setIcon(multiStateIcon);
btn->setCheckable(true); // 启用选中状态
```

### 3. 高级用法

#### 直接使用IconFontManager
```cpp
#include "src/components/iconfont/iconfontmanager.h"

IconFontManager* manager = IconFontManager::instance();

// 加载额外的字体文件
manager->loadFont("/path/to/another/font.ttf", "AnotherFont");

// 创建自定义图标
QPixmap pixmap = manager->createPixmap(0xe001, QSize(48, 48), QColor(Qt::blue));
```

#### 缓存管理
```cpp
IconFontManager* manager = IconFontManager::instance();

// 清除缓存
manager->clearCache();

// 禁用缓存（不推荐，会影响性能）
manager->setCacheEnabled(false);
```

## 可用图标

基于智慧黑板iconfont项目，包含以下分类的图标：

### 基础操作图标
- `Icons::SAVE` - 保存
- `Icons::EXIT` - 退出  
- `Icons::REFRESH` - 刷新
- `Icons::SETTING` - 设置
- `Icons::SHUTDOWN` - 关机
- `Icons::CLOSE` - 关闭

### 编辑操作图标
- `Icons::UNDO` - 撤销
- `Icons::DELETE` - 删除
- `Icons::CLEAR` - 清空
- `Icons::SELECT` - 选择
- `Icons::PLUS` - 加号
- `Icons::MINUS` - 减号

### 绘图工具图标
- `Icons::SELECT_TOOL` - 选择工具
- `Icons::PEN_TOOL` - 画笔
- `Icons::PEN_MARKER` - 马克笔
- `Icons::PEN_WATERCOLOR` - 水彩笔
- `Icons::LINE_TOOL` - 直线
- `Icons::ERASER` - 橡皮擦

### 几何图形图标
- `Icons::CIRCLE` - 圆形
- `Icons::RECTANGLE` - 矩形
- `Icons::TRIANGLE` - 三角形
- `Icons::ARROW` - 箭头

### 界面控制图标
- `Icons::FULLSCREEN` - 全屏
- `Icons::MINIMIZE` - 最小化
- `Icons::ROTATE` - 旋转
- `Icons::ARROW_LEFT/RIGHT/DOWN` - 方向箭头

更多图标请查看 `iconfonts.h` 中的 `Icons` 命名空间。

## 性能优化

1. **缓存机制**: 系统自动缓存生成的图标，避免重复渲染
2. **智能大小限制**: 缓存大小限制为200个图标，适合普通应用
3. **资源管理**: 字体文件嵌入到资源中，无需外部依赖

## 注意事项

1. 确保在使用图标前调用 `IconFonts::initialize()`
2. 建议在应用程序启动时一次性初始化
3. Unicode值基于实际的iconfont项目，如需更新请修改 `iconfonts.h`
4. 支持高DPI屏幕，图标会自动适配

## 文件结构

```
src/components/iconfont/
├── iconfontmanager.h          # 核心管理器头文件
├── iconfontmanager.cpp        # 核心管理器实现
├── iconfonts.h                # 便捷接口和图标定义
├── iconfonts.cpp              # 便捷接口实现
├── example_usage.cpp          # 使用示例
└── README.md                  # 本文档
```

## 故障排除

### 图标不显示
1. 检查是否调用了 `IconFonts::initialize()`
2. 确认字体文件已正确添加到资源系统
3. 检查Unicode值是否正确

### 性能问题
1. 确保缓存已启用（默认启用）
2. 避免频繁创建大尺寸图标
3. 考虑预加载常用图标

### 编译错误
1. 确保项目包含了所有相关文件
2. 检查CMakeLists.txt是否正确配置
3. 确认Qt版本支持（Qt 5.12+推荐）
