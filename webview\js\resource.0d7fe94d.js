import { $ as $post } from "./axios.7d58980a.js";
const sysUrl = "/zhhb-system";
function getTeachPlanList(data = {}) {
  return $post(
    `${sysUrl}/v1/teaching/teach_plan_list`,
    data
  );
}
function getResourceTree(data = {}) {
  return $post(`${sysUrl}/v1/teaching/teach_plan/directory_tree`, data);
}
function resourceUpload(data = {}) {
  return $post(`${sysUrl}/v1/teaching/resource_upload`, data);
}
function getUploadStatus(data = {}) {
  return $post(`${sysUrl}/v1/teaching/upload_transfer_result`, data);
}
function recordResourceProgress(data = {}) {
  return $post(`${sysUrl}/v1/teaching/record_progress`, data);
}
function getResourceProgressList(data = {}) {
  return $post(`${sysUrl}/v1/teaching/progress_list`, data, {
    closeErrorToast: true
  });
}
function recordResourceTree(data = {}) {
  return $post(`${sysUrl}/v1/teaching/record_teach_index`, data);
}
function getResourceTreeRecord(data = {}) {
  return $post(`${sysUrl}/v1/teaching/get_teach_index`, data);
}
function genQrcodeShareTempId(data) {
  return $post(`${sysUrl}/v1/qrcode/board/temp_add`, data);
}
function syncPrepareLessons(data) {
  return $post(`${sysUrl}/v1/qrcode/board/temp_sync`, data);
}
export {
  getResourceProgressList as a,
  getResourceTree as b,
  recordResourceTree as c,
  getTeachPlanList as d,
  getResourceTreeRecord as e,
  genQrcodeShareTempId as f,
  getUploadStatus as g,
  recordResourceProgress as h,
  resourceUpload as r,
  syncPrepareLessons as s
};
