# 屏幕适配管理器

## 概述

ScreenAdaptationManager 是一个统一的多屏幕尺寸适配解决方案，支持1080p、4K等不同分辨率的自动适配。

## 设计基准

**基准分辨率：4096x2160 (Cinema 4K)**

选择Cinema 4K作为设计基准的原因：
1. **FloatMenu原始设计**：代码中明确注释"特别针对4K屏幕优化"
2. **更宽的适配范围**：4096px比标准UHD 4K的3840px更宽，提供更好的适配效果
3. **向下兼容**：以4K为基准，可以很好地向下适配到1080p等较小分辨率
4. **未来趋势**：4K屏幕越来越普及，以4K为基准更符合未来发展

## 支持的屏幕类型

| 屏幕类型 | 分辨率 | 缩放因子示例 |
|---------|--------|-------------|
| HD 1080p | 1920x1080 | ~0.47 |
| QHD 1440p | 2560x1440 | ~0.63 |
| UHD 4K | 3840x2160 | ~0.94 |
| Cinema 4K | 4096x2160 | 1.0 |
| UHD 5K | 5120x2880 | ~1.25 |
| 自定义 | 其他分辨率 | 按比例计算 |

## 使用方法

### 1. 初始化（在main.cpp中）

```cpp
#include "src/screen_adaptation/ScreenAdaptationManager.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    
    // 初始化屏幕适配管理器
    ScreenAdaptationConstants::ScreenAdaptationManager::instance()->initialize();
    
    // ... 其他初始化代码
}
```

### 2. 获取适配后的尺寸

```cpp
#include "src/screen_adaptation/ScreenAdaptationManager.h"

// 方法一：使用便捷函数
int adaptedWidth = ScreenAdaptationConstants::adaptSize(207);  // 原始207px
QSize adaptedSize = ScreenAdaptationConstants::adaptSize(QSize(207, 188));

// 方法二：使用管理器实例
auto* manager = ScreenAdaptationConstants::ScreenAdaptationManager::instance();
int adaptedHeight = manager->getAdaptedSize(188);
qreal adaptedRadius = manager->getAdaptedSize(65.0);
```

### 3. 获取屏幕信息

```cpp
auto* manager = ScreenAdaptationConstants::ScreenAdaptationManager::instance();

// 获取屏幕类型
auto screenType = manager->getScreenType();
QString typeString = manager->getScreenTypeString();

// 获取缩放信息
qreal scaleFactor = manager->getScaleFactor();
qreal devicePixelRatio = manager->getDevicePixelRatio();

// 检查屏幕特性
bool isHighDPI = manager->isHighDPI();
bool is4K = manager->is4K();
```

## 在FloatMenu中的应用

FloatMenuConstants.h 已经集成了屏幕适配功能：

```cpp
// 获取适配后的尺寸（推荐）
int coreSize = FloatMenuConstants::getCoreWidgetSize();
int buttonSize = FloatMenuConstants::getToolButtonSize();

// 使用原始常量（向后兼容）
int originalSize = FloatMenuConstants::CORE_WIDGET_SIZE;  // 仍然可用
```

## 缩放算法

```cpp
// 计算缩放因子
qreal widthRatio = 当前屏幕宽度 / 4096;
qreal heightRatio = 当前屏幕高度 / 2160;
qreal baseScale = qMin(widthRatio, heightRatio);  // 使用较小值确保不超出屏幕
qreal dpiScale = 逻辑DPI / 96.0;
qreal finalScale = baseScale * dpiScale;

// 限制范围
finalScale = qBound(0.3, finalScale, 3.0);
```

## 实际效果示例

以FloatMenu核心圆盘（原始207px）为例：

| 屏幕分辨率 | 缩放因子 | 显示尺寸 |
|-----------|---------|---------|
| 1920x1080 | 0.47 | ~97px |
| 2560x1440 | 0.63 | ~130px |
| 3840x2160 | 0.94 | ~195px |
| 4096x2160 | 1.0 | 207px |

## 调试信息

初始化时会输出详细的调试信息：

```
=== 屏幕适配管理器初始化 ===
屏幕尺寸: QSize(1920, 1080)
屏幕类型: 1080p (1920x1080)
设备像素比例: 1
逻辑DPI: 96
物理DPI: 96
计算的缩放因子: 0.46875
是否高DPI: false
========================
缩放计算详情:
  宽度比例: 0.46875
  高度比例: 0.5
  基础缩放: 0.46875
  DPI缩放: 1
  最终缩放因子: 0.46875
```

## 注意事项

1. **初始化时机**：必须在QApplication创建后调用initialize()
2. **线程安全**：ScreenAdaptationManager是单例，线程安全
3. **动态更新**：屏幕分辨率改变时可调用updateScreenInfo()重新计算
4. **向后兼容**：现有代码无需修改，原始常量仍然可用

## 扩展性

如需添加新的屏幕类型或修改缩放算法，只需修改：
- `determineScreenType()` - 屏幕类型判断
- `calculateScaleFactor()` - 缩放因子计算
- `getScreenTypeString()` - 类型描述
