/**
 * @license BSD
 * @copyright 2014-2025 <EMAIL>
 * 
 * Project Home:
 *   https://github.com/hizzgdev/jsmind/
 */

/* important section */
.jsmind-inner {
    position: relative;
    overflow: auto;
    width: 100%;
    height: 100%;
    outline: none;
} /*box-shadow:0 0 2px #000;*/
.jsmind-inner {
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.jsmind-inner canvas {
    position: absolute;
}

/* z-index:1 */
svg.jsmind {
    position: absolute;
    z-index: 1;
}
canvas.jsmind {
    position: absolute;
    z-index: 1;
}

/* z-index:2 */
jmnodes {
    position: absolute;
    z-index: 2;
    background-color: rgba(0, 0, 0, 0);
} /*background color is necessary*/
jmnode {
    position: absolute;
    cursor: default;
    max-width: 33.33333rem;
}
jmexpander {
    position: absolute;
    width: 0.91667rem;
    height: 0.91667rem;
    display: block;
    overflow: hidden;
    line-height: 1rem;
    font-size: 0.83333rem;
    text-align: center;
    border-radius: 0.5rem;
    border-width: 0.08333rem;
    border-style: solid;
    cursor: pointer;
}

.jmnode-overflow-wrap jmnodes {
    min-width: 35rem;
}

.jmnode-overflow-hidden jmnode {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* default theme */
jmnode {
    padding: 0.83333rem;
    background-color: #fff;
    color: #333;
    border-radius: 0.41667rem;
    box-shadow: 0.08333rem 0.08333rem 0.08333rem #666;
    font: 1.33333rem/1.125 Verdana, Arial, Helvetica, sans-serif;
}
jmnode:hover {
    box-shadow: 0.16667rem 0.16667rem 0.66667rem #000;
    background-color: #ebebeb;
    color: #333;
}
jmnode.selected {
    background-color: #11f;
    color: #fff;
    box-shadow: 0.16667rem 0.16667rem 0.66667rem #000;
}
jmnode.root {
    font-size: 2rem;
}
jmexpander {
    border-color: gray;
}
jmexpander:hover {
    border-color: #000;
}

@media screen and (max-device-width: 1024px) {
    jmnode {
        padding: 0.41667rem;
        border-radius: 0.25rem;
        font-size: 1.16667rem;
    }
    jmnode.root {
        font-size: 1.75rem;
    }
}
/* primary theme */
jmnodes.theme-primary jmnode {
    background-color: #428bca;
    color: #fff;
    border-color: #357ebd;
}
jmnodes.theme-primary jmnode:hover {
    background-color: #3276b1;
    border-color: #285e8e;
}
jmnodes.theme-primary jmnode.selected {
    background-color: #f1c40f;
    color: #fff;
}
jmnodes.theme-primary jmnode.root {
}
jmnodes.theme-primary jmexpander {
}
jmnodes.theme-primary jmexpander:hover {
}

/* warning theme */
jmnodes.theme-warning jmnode {
    background-color: #f0ad4e;
    border-color: #eea236;
    color: #fff;
}
jmnodes.theme-warning jmnode:hover {
    background-color: #ed9c28;
    border-color: #d58512;
}
jmnodes.theme-warning jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-warning jmnode.root {
}
jmnodes.theme-warning jmexpander {
}
jmnodes.theme-warning jmexpander:hover {
}

/* danger theme */
jmnodes.theme-danger jmnode {
    background-color: #d9534f;
    border-color: #d43f3a;
    color: #fff;
}
jmnodes.theme-danger jmnode:hover {
    background-color: #d2322d;
    border-color: #ac2925;
}
jmnodes.theme-danger jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-danger jmnode.root {
}
jmnodes.theme-danger jmexpander {
}
jmnodes.theme-danger jmexpander:hover {
}

/* success theme */
jmnodes.theme-success jmnode {
    background-color: #5cb85c;
    border-color: #4cae4c;
    color: #fff;
}
jmnodes.theme-success jmnode:hover {
    background-color: #47a447;
    border-color: #398439;
}
jmnodes.theme-success jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-success jmnode.root {
}
jmnodes.theme-success jmexpander {
}
jmnodes.theme-success jmexpander:hover {
}

/* info theme */
jmnodes.theme-info jmnode {
    background-color: #5dc0de;
    border-color: #46b8da;
    color: #fff;
}
jmnodes.theme-info jmnode:hover {
    background-color: #39b3d7;
    border-color: #269abc;
}
jmnodes.theme-info jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-info jmnode.root {
}
jmnodes.theme-info jmexpander {
}
jmnodes.theme-info jmexpander:hover {
}

/* greensea theme */
jmnodes.theme-greensea jmnode {
    background-color: #1abc9c;
    color: #fff;
}
jmnodes.theme-greensea jmnode:hover {
    background-color: #16a085;
}
jmnodes.theme-greensea jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-greensea jmnode.root {
}
jmnodes.theme-greensea jmexpander {
}
jmnodes.theme-greensea jmexpander:hover {
}

/* nephrite theme */
jmnodes.theme-nephrite jmnode {
    background-color: #2ecc71;
    color: #fff;
}
jmnodes.theme-nephrite jmnode:hover {
    background-color: #27ae60;
}
jmnodes.theme-nephrite jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-nephrite jmnode.root {
}
jmnodes.theme-nephrite jmexpander {
}
jmnodes.theme-nephrite jmexpander:hover {
}

/* belizehole theme */
jmnodes.theme-belizehole jmnode {
    background-color: #3498db;
    color: #fff;
}
jmnodes.theme-belizehole jmnode:hover {
    background-color: #2980b9;
}
jmnodes.theme-belizehole jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-belizehole jmnode.root {
}
jmnodes.theme-belizehole jmexpander {
}
jmnodes.theme-belizehole jmexpander:hover {
}

/* wisteria theme */
jmnodes.theme-wisteria jmnode {
    background-color: #9b59b6;
    color: #fff;
}
jmnodes.theme-wisteria jmnode:hover {
    background-color: #8e44ad;
}
jmnodes.theme-wisteria jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-wisteria jmnode.root {
}
jmnodes.theme-wisteria jmexpander {
}
jmnodes.theme-wisteria jmexpander:hover {
}

/* asphalt theme */
jmnodes.theme-asphalt jmnode {
    background-color: #34495e;
    color: #fff;
}
jmnodes.theme-asphalt jmnode:hover {
    background-color: #2c3e50;
}
jmnodes.theme-asphalt jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-asphalt jmnode.root {
}
jmnodes.theme-asphalt jmexpander {
}
jmnodes.theme-asphalt jmexpander:hover {
}

/* orange theme */
jmnodes.theme-orange jmnode {
    background-color: #f1c40f;
    color: #fff;
}
jmnodes.theme-orange jmnode:hover {
    background-color: #f39c12;
}
jmnodes.theme-orange jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-orange jmnode.root {
}
jmnodes.theme-orange jmexpander {
}
jmnodes.theme-orange jmexpander:hover {
}

/* pumpkin theme */
jmnodes.theme-pumpkin jmnode {
    background-color: #e67e22;
    color: #fff;
}
jmnodes.theme-pumpkin jmnode:hover {
    background-color: #d35400;
}
jmnodes.theme-pumpkin jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-pumpkin jmnode.root {
}
jmnodes.theme-pumpkin jmexpander {
}
jmnodes.theme-pumpkin jmexpander:hover {
}

/* pomegranate theme */
jmnodes.theme-pomegranate jmnode {
    background-color: #e74c3c;
    color: #fff;
}
jmnodes.theme-pomegranate jmnode:hover {
    background-color: #c0392b;
}
jmnodes.theme-pomegranate jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-pomegranate jmnode.root {
}
jmnodes.theme-pomegranate jmexpander {
}
jmnodes.theme-pomegranate jmexpander:hover {
}

/* clouds theme */
jmnodes.theme-clouds jmnode {
    background-color: #ecf0f1;
    color: #333;
}
jmnodes.theme-clouds jmnode:hover {
    background-color: #bdc3c7;
}
jmnodes.theme-clouds jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-clouds jmnode.root {
}
jmnodes.theme-clouds jmexpander {
}
jmnodes.theme-clouds jmexpander:hover {
}

/* asbestos theme */
jmnodes.theme-asbestos jmnode {
    background-color: #95a5a6;
    color: #fff;
}
jmnodes.theme-asbestos jmnode:hover {
    background-color: #7f8c8d;
}
jmnodes.theme-asbestos jmnode.selected {
    background-color: #11f;
    color: #fff;
}
jmnodes.theme-asbestos jmnode.root {
}
jmnodes.theme-asbestos jmexpander {
}
jmnodes.theme-asbestos jmexpander:hover {
}

.mind-node-container[data-v-04dca7b5] {
  width: 100%;
  height: 100%;
}
/* #jsmind_container {
  width: 100%;
  height: 100%;
} */

/* 隐藏滚动条 */
.mind-node-container[data-v-04dca7b5] .jsmind-inner {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.mind-node-container[data-v-04dca7b5] .jsmind-inner::-webkit-scrollbar {
  display: none;
}

/* 确保截图时内容完整显示 */
.mind-node-container.capturing[data-v-04dca7b5] {
  overflow: visible !important;
}
.mind-node-container.capturing[data-v-04dca7b5] * {
  overflow: visible !important;
}
